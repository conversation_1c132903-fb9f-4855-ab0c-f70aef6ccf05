# Database Configuration
DATABASE_URL=postgresql://dahoai:dahoai@localhost:5432/dahoai

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=DahoAI

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost:3000"]

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://localhost:8000

# Development Configuration
DEBUG=true
ENVIRONMENT=development
