# GitHub Copilot Instructions for DahoAI Platform

## Project Overview
DahoAI is a comprehensive AI agents platform that provides users with access to specialized AI agents for business and personal needs. The platform features an agent marketplace, deployment environment, and user dashboard built with Python/FastAPI backend and React/TypeScript frontend.

## Architecture
- **Backend**: Python 3.11, FastAPI, PostgreSQL, Redis, Kubernetes
- **Frontend**: TypeScript, React/Next.js, RESTful API integration
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Caching**: Redis for session and API caching
- **Deployment**: Docker containers with Kubernetes orchestration
- **Authentication**: JWT with OAuth 2.0 support

## Key Components

### Core Services
- **User Management**: Registration, authentication, profiles, subscriptions
- **Agent Marketplace**: Agent discovery, deployment, management
- **Task Execution**: Agent task creation, monitoring, results
- **Organization Management**: Team collaboration, role-based access
- **API Gateway**: External integrations, webhooks, rate limiting

### Data Models
- **User**: Authentication, profile, subscription management
- **Agent**: Marketplace listings, versions, capabilities
- **Task**: User requests, execution tracking, results
- **Organization**: Team management, shared resources
- **Deployment**: Agent instances, configuration, monitoring

## Development Guidelines

### Backend (Python/FastAPI)
```python
# Use async/await for all API endpoints
@app.post("/api/v1/tasks", response_model=TaskResponse)
async def create_task(
    request: TaskCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
) -> TaskResponse:
    # Implementation here
    pass

# Use Pydantic models for request/response validation
class TaskCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., min_length=1, max_length=5000)
    agent_id: UUID
    priority: TaskPriority = TaskPriority.MEDIUM

# Use SQLAlchemy with async support
async def get_user_tasks(
    db: AsyncSession,
    user_id: UUID,
    skip: int = 0,
    limit: int = 20
) -> List[Task]:
    query = select(Task).where(Task.creator_id == user_id)
    result = await db.execute(query.offset(skip).limit(limit))
    return result.scalars().all()
```

### Frontend (TypeScript/React)
```typescript
// Use functional components with hooks
const TaskList: React.FC<TaskListProps> = ({ userId }) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchUserTasks(userId);
  }, [userId]);

  const fetchUserTasks = async (userId: string) => {
    setLoading(true);
    try {
      const response = await api.get(`/users/${userId}/tasks`);
      setTasks(response.data.tasks);
    } catch (error) {
      console.error('Failed to fetch tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="task-list">
      {loading ? (
        <Spinner />
      ) : (
        tasks.map(task => (
          <TaskCard key={task.id} task={task} />
        ))
      )}
    </div>
  );
};

// Use TypeScript interfaces for type safety
interface Task {
  id: string;
  title: string;
  description: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  created_at: string;
  updated_at: string;
}
```

## API Patterns

### RESTful Endpoints
- `GET /api/v1/agents` - List agents with filtering/pagination
- `POST /api/v1/agents` - Create new agent
- `GET /api/v1/agents/{id}` - Get agent details
- `POST /api/v1/deployments` - Deploy agent
- `GET /api/v1/tasks` - List user tasks
- `POST /api/v1/tasks` - Create task
- `PUT /api/v1/tasks/{id}` - Update task status

### Response Format
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed",
  "timestamp": "2025-01-14T10:30:00Z"
}
```

### Error Handling
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "reason": "Invalid email format"
    }
  },
  "timestamp": "2025-01-14T10:30:00Z"
}
```

## Security Best Practices

### Authentication
- Use JWT tokens with expiration
- Implement refresh token rotation
- Validate tokens on every request
- Use secure HTTP-only cookies for web clients

### Authorization
- Implement role-based access control (RBAC)
- Check permissions at service layer
- Use organization-based data isolation
- Audit all sensitive operations

### Data Protection
- Encrypt sensitive data at rest
- Use HTTPS for all communications
- Implement rate limiting
- Validate all input data
- Sanitize database queries

## Testing Strategy

### Unit Tests
```python
# Backend unit tests
def test_create_task_validation():
    # Test input validation
    pass

def test_task_status_transition():
    # Test state machine logic
    pass
```

### Integration Tests
```python
# API integration tests
async def test_create_task_flow(client, db_session):
    # Test complete task creation flow
    pass

async def test_agent_deployment_flow(client, db_session):
    # Test agent deployment and monitoring
    pass
```

### Frontend Tests
```typescript
// Component tests
describe('TaskList', () => {
  it('renders loading state', () => {
    // Test loading UI
  });

  it('displays tasks correctly', () => {
    // Test task rendering
  });
});
```

## Performance Considerations

### Database Optimization
- Use appropriate indexes
- Implement query optimization
- Use connection pooling
- Consider read replicas for analytics

### Caching Strategy
- Cache frequently accessed data
- Use Redis for session storage
- Implement cache invalidation
- Monitor cache hit rates

### API Optimization
- Implement pagination for large datasets
- Use compression for responses
- Implement request/response caching
- Monitor API performance metrics

## Deployment Pipeline

### Development
- Local development with Docker Compose
- Hot reload for both backend and frontend
- Automated testing on commits
- Code quality checks (linting, formatting)

### Staging
- Automated deployment on merge to main
- Integration testing against staging APIs
- Performance testing
- Security scanning

### Production
- Blue-green deployments
- Automated rollback capabilities
- Monitoring and alerting
- Backup and disaster recovery

## Monitoring and Observability

### Application Metrics
- API response times
- Error rates
- Database query performance
- Cache hit rates

### Business Metrics
- User registration/conversion rates
- Agent deployment frequency
- Task completion rates
- Revenue metrics

### Infrastructure Monitoring
- Server resource utilization
- Database performance
- Network latency
- Container health

## Code Quality Standards

### Python Backend
- Use type hints for all function parameters
- Follow PEP 8 style guidelines
- Write comprehensive docstrings
- Use async/await for I/O operations
- Implement proper error handling

### TypeScript Frontend
- Use strict TypeScript configuration
- Follow React best practices
- Implement proper component composition
- Use custom hooks for shared logic
- Write unit tests for components

### General
- Write self-documenting code
- Use meaningful variable names
- Implement proper logging
- Follow SOLID principles
- Keep functions small and focused

## Git Workflow

### Branch Strategy
- `main`: Production-ready code
- `develop`: Integration branch
- `feature/*`: Feature development
- `hotfix/*`: Critical bug fixes

### Commit Messages
- Use conventional commit format
- Include ticket references
- Write clear, descriptive messages
- Keep commits atomic

### Code Review
- Require at least one approval
- Review for functionality, performance, security
- Check test coverage
- Verify documentation updates

This instruction set provides comprehensive guidance for developing the DahoAI platform. Follow these patterns and practices to ensure consistent, maintainable, and scalable code.