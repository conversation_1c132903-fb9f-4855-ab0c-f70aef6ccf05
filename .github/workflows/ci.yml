name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Backend Tests and Linting
  backend-test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: dahoai
          POSTGRES_USER: dahoai
          POSTGRES_DB: dahoai
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run Black formatting check
      working-directory: ./backend
      run: black --check .
    
    - name: Run Flake8 linting
      working-directory: ./backend
      run: flake8 .
    
    - name: Run MyPy type checking
      working-directory: ./backend
      run: mypy src/
    
    - name: Run tests
      working-directory: ./backend
      env:
        DATABASE_URL: postgresql://dahoai:dahoai@localhost:5432/dahoai
        REDIS_URL: redis://localhost:6379
      run: pytest

  # Frontend Tests and Linting
  frontend-test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run ESLint
      working-directory: ./frontend
      run: npm run lint
    
    - name: Check Prettier formatting
      working-directory: ./frontend
      run: npx prettier --check .
    
    - name: Run TypeScript check
      working-directory: ./frontend
      run: npx tsc --noEmit
    
    - name: Run tests
      working-directory: ./frontend
      run: npm test
    
    - name: Build application
      working-directory: ./frontend
      run: npm run build

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Docker Build Test
  docker-build:
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build backend Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: false
        tags: dahoai-backend:test
    
    - name: Build frontend Docker image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: false
        tags: dahoai-frontend:test
