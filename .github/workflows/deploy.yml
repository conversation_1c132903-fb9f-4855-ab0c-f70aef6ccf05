name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta-backend
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/${{ github.repository }}/backend
        tags: |
          type=ref,event=branch
          type=ref,event=tag
          type=sha
    
    - name: Extract metadata
      id: meta-frontend
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/${{ github.repository }}/frontend
        tags: |
          type=ref,event=branch
          type=ref,event=tag
          type=sha
    
    - name: Build and push backend
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ steps.meta-backend.outputs.tags }}
        labels: ${{ steps.meta-backend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push frontend
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: ${{ steps.meta-frontend.outputs.tags }}
        labels: ${{ steps.meta-frontend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    # Add deployment steps here based on your infrastructure
    # Examples: Kubernetes, AWS ECS, Azure Container Instances, etc.
    
    - name: Deploy to staging (placeholder)
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
    
    - name: Deploy to production (placeholder)
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
