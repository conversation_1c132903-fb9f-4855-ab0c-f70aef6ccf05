.PHONY: help install dev test lint format clean docker-up docker-down

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install all dependencies
	@echo "Installing backend dependencies..."
	cd backend && pip install -r requirements.txt
	@echo "Installing frontend dependencies..."
	cd frontend && npm install

dev: ## Start development environment
	docker-compose up -d

dev-logs: ## Show development logs
	docker-compose logs -f

test: ## Run all tests
	@echo "Running backend tests..."
	cd backend && pytest
	@echo "Running frontend tests..."
	cd frontend && npm test

lint: ## Run linting for all projects
	@echo "Linting backend..."
	cd backend && flake8 . && mypy src/
	@echo "Linting frontend..."
	cd frontend && npm run lint

format: ## Format code for all projects
	@echo "Formatting backend..."
	cd backend && black .
	@echo "Formatting frontend..."
	cd frontend && npx prettier --write .

clean: ## Clean up generated files
	@echo "Cleaning backend..."
	cd backend && find . -type d -name __pycache__ -delete
	cd backend && find . -name "*.pyc" -delete
	@echo "Cleaning frontend..."
	cd frontend && rm -rf .next node_modules/.cache

docker-up: ## Start Docker containers
	docker-compose up -d

docker-down: ## Stop Docker containers
	docker-compose down

docker-logs: ## Show Docker logs
	docker-compose logs -f

docker-build: ## Build Docker images
	docker-compose build

migrate: ## Run database migrations
	cd backend && alembic upgrade head

migrate-create: ## Create new migration
	cd backend && alembic revision --autogenerate -m "$(name)"
