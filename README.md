# DahoAI - AI Agents Platform

A comprehensive AI agents platform that provides users with access to specialized AI agents for business and personal needs. The platform features an agent marketplace, deployment environment, and user dashboard.

## Architecture

- **Backend**: Python 3.11, FastAPI, PostgreSQL, Redis, Kubernetes
- **Frontend**: TypeScript, React/Next.js, RESTful API integration
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Caching**: Redis for session and API caching
- **Deployment**: Docker containers with Kubernetes orchestration
- **Authentication**: JWT with OAuth 2.0 support

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Python 3.11+ (for local development)
- Node.js 18+ (for local development)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dahoai
   ```

2. **Start services with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Local Development

#### Backend Development

1. **Set up Python environment**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Run database migrations**
   ```bash
   alembic upgrade head
   ```

3. **Start the development server**
   ```bash
   uvicorn src.main:app --reload
   ```

4. **Run tests**
   ```bash
   pytest
   ```

5. **Code quality checks**
   ```bash
   black .
   flake8 .
   mypy src/
   ```

#### Frontend Development

1. **Install dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Start the development server**
   ```bash
   npm run dev
   ```

3. **Run tests**
   ```bash
   npm test
   ```

4. **Code quality checks**
   ```bash
   npm run lint
   npx prettier --check .
   ```

## Project Structure

```
dahoai/
├── backend/                 # Python FastAPI backend
│   ├── src/
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   ├── api/           # API routes
│   │   └── main.py        # FastAPI app
│   ├── tests/             # Backend tests
│   ├── alembic/           # Database migrations
│   └── requirements.txt   # Python dependencies
├── frontend/               # Next.js frontend
│   ├── src/
│   │   ├── app/           # Next.js app router
│   │   ├── components/    # React components
│   │   └── services/      # API services
│   ├── tests/             # Frontend tests
│   └── package.json       # Node.js dependencies
├── .github/workflows/      # CI/CD pipelines
└── docker-compose.yml     # Development environment
```

## Key Features

### Core Services
- **User Management**: Registration, authentication, profiles, subscriptions
- **Agent Marketplace**: Agent discovery, deployment, management
- **Task Execution**: Agent task creation, monitoring, results
- **Organization Management**: Team collaboration, role-based access
- **API Gateway**: External integrations, webhooks, rate limiting

### Data Models
- **User**: Authentication, profile, subscription management
- **Agent**: Marketplace listings, versions, capabilities
- **Task**: User requests, execution tracking, results
- **Organization**: Team management, shared resources
- **Deployment**: Agent instances, configuration, monitoring

## API Documentation

The API documentation is automatically generated and available at:
- Development: http://localhost:8000/docs
- Production: https://api.dahoai.com/docs

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests and linting
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
