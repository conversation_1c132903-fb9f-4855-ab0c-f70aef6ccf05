[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':'
    E203,
    # W503: line break before binary operator
    W503,
    # E501: line too long (handled by black)
    E501
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    *.egg-info,
    alembic/versions
per-file-ignores =
    # F401: imported but unused
    __init__.py:F401
    # F403: star import used
    alembic/env.py:F403,F401
