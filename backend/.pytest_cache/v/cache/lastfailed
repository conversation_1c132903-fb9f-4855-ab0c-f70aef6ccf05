{"tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_successful": true, "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_paid_model": true, "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_missing_required_fields": true, "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_invalid_pricing_model": true, "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_unauthenticated": true, "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_duplicate_name": true, "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_by_id": true, "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_not_found": true, "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_invalid_uuid": true, "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_empty_uuid": true, "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_malformed_uuid": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_list": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_with_category_filter": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_with_search_filter": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_featured_only": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_pagination": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_invalid_page": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_invalid_limit": true, "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_empty_result": true, "tests/contract/test_auth_login.py::TestAuthLogin::test_login_successful": true, "tests/contract/test_auth_login.py::TestAuthLogin::test_login_invalid_credentials": true, "tests/contract/test_auth_login.py::TestAuthLogin::test_login_missing_email": true, "tests/contract/test_auth_login.py::TestAuthLogin::test_login_missing_password": true, "tests/contract/test_auth_login.py::TestAuthLogin::test_login_invalid_email_format": true, "tests/contract/test_auth_login.py::TestAuthLogin::test_login_short_password": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_successful": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_duplicate_email": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_duplicate_username": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_missing_required_fields": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_invalid_email": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_short_password": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_short_username": true, "tests/contract/test_auth_register.py::TestAuthRegister::test_register_short_full_name": true, "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_successful": true, "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_minimal_config": true, "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_missing_agent_id": true, "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_invalid_agent_id": true, "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_nonexistent_agent": true, "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_unauthenticated": true, "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_invalid_config": true, "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_successful": true, "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_not_found": true, "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_not_owned_by_user": true, "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_invalid_uuid": true, "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_unauthenticated": true, "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_invalid_token": true, "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_already_stopped": true, "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_by_id": true, "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_not_found": true, "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_not_owned_by_user": true, "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_invalid_uuid": true, "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_unauthenticated": true, "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_invalid_token": true, "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_list": true, "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_unauthenticated": true, "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_invalid_token": true, "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_empty_list": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_successful": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_minimal_data": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_missing_rating": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_invalid_rating_low": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_invalid_rating_high": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_title_too_long": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_comment_too_long": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_invalid_agent_id": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_nonexistent_agent": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_unauthenticated": true, "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_duplicate": true, "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews": true, "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_pagination": true, "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_invalid_agent_id": true, "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_nonexistent_agent": true, "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_invalid_page": true, "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_invalid_limit": true, "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_empty_result": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_successful": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_minimal_data": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_missing_required_fields": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_invalid_agent_id": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_invalid_priority": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_title_too_long": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_description_too_long": true, "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_unauthenticated": true, "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_by_id": true, "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_not_found": true, "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_not_owned_by_user": true, "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_invalid_uuid": true, "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_unauthenticated": true, "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_invalid_token": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_list": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_with_status_filter": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_pagination": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_invalid_status": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_invalid_page": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_invalid_limit": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_unauthenticated": true, "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_empty_list": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_status": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_priority": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_multiple_fields": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_empty_data": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_invalid_status": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_invalid_priority": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_not_found": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_not_owned_by_user": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_invalid_uuid": true, "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_unauthenticated": true, "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_profile": true, "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_unauthenticated": true, "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_invalid_token": true, "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_expired_token": true, "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_profile": true, "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_partial_data": true, "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_empty_data": true, "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_unauthenticated": true, "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_invalid_token": true, "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_username_conflict": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_task_completed": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_task_failed": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_deployment_status_changed": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_missing_event": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_missing_data": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_empty_payload": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_large_payload": true, "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_unsupported_content_type": true}