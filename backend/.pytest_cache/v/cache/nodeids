["tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_duplicate_name", "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_invalid_pricing_model", "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_missing_required_fields", "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_paid_model", "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_successful", "tests/contract/test_agents_create.py::TestAgentsCreate::test_create_agent_unauthenticated", "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_by_id", "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_empty_uuid", "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_invalid_uuid", "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_malformed_uuid", "tests/contract/test_agents_get.py::TestAgentsGet::test_get_agent_not_found", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_empty_result", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_featured_only", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_invalid_limit", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_invalid_page", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_list", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_pagination", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_with_category_filter", "tests/contract/test_agents_list.py::TestAgentsList::test_get_agents_with_search_filter", "tests/contract/test_auth_login.py::TestAuthLogin::test_login_invalid_credentials", "tests/contract/test_auth_login.py::TestAuthLogin::test_login_invalid_email_format", "tests/contract/test_auth_login.py::TestAuthLogin::test_login_missing_email", "tests/contract/test_auth_login.py::TestAuthLogin::test_login_missing_password", "tests/contract/test_auth_login.py::TestAuthLogin::test_login_short_password", "tests/contract/test_auth_login.py::TestAuthLogin::test_login_successful", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_duplicate_email", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_duplicate_username", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_invalid_email", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_missing_required_fields", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_short_full_name", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_short_password", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_short_username", "tests/contract/test_auth_register.py::TestAuthRegister::test_register_successful", "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_invalid_agent_id", "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_invalid_config", "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_minimal_config", "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_missing_agent_id", "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_nonexistent_agent", "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_successful", "tests/contract/test_deployments_create.py::TestDeploymentsCreate::test_create_deployment_unauthenticated", "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_already_stopped", "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_invalid_token", "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_invalid_uuid", "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_not_found", "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_not_owned_by_user", "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_successful", "tests/contract/test_deployments_delete.py::TestDeploymentsDelete::test_delete_deployment_unauthenticated", "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_by_id", "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_invalid_token", "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_invalid_uuid", "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_not_found", "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_not_owned_by_user", "tests/contract/test_deployments_get.py::TestDeploymentsGet::test_get_deployment_unauthenticated", "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_empty_list", "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_invalid_token", "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_list", "tests/contract/test_deployments_list.py::TestDeploymentsList::test_get_deployments_unauthenticated", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_comment_too_long", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_duplicate", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_invalid_agent_id", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_invalid_rating_high", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_invalid_rating_low", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_minimal_data", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_missing_rating", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_nonexistent_agent", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_successful", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_title_too_long", "tests/contract/test_reviews_create.py::TestReviewsCreate::test_create_review_unauthenticated", "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews", "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_empty_result", "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_invalid_agent_id", "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_invalid_limit", "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_invalid_page", "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_nonexistent_agent", "tests/contract/test_reviews_list.py::TestReviewsList::test_get_agent_reviews_pagination", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_description_too_long", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_invalid_agent_id", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_invalid_priority", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_minimal_data", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_missing_required_fields", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_successful", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_title_too_long", "tests/contract/test_tasks_create.py::TestTasksCreate::test_create_task_unauthenticated", "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_by_id", "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_invalid_token", "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_invalid_uuid", "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_not_found", "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_not_owned_by_user", "tests/contract/test_tasks_get.py::TestTasksGet::test_get_task_unauthenticated", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_empty_list", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_invalid_limit", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_invalid_page", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_invalid_status", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_list", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_pagination", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_unauthenticated", "tests/contract/test_tasks_list.py::TestTasksList::test_get_tasks_with_status_filter", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_empty_data", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_invalid_priority", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_invalid_status", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_invalid_uuid", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_multiple_fields", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_not_found", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_not_owned_by_user", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_priority", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_status", "tests/contract/test_tasks_update.py::TestTasksUpdate::test_update_task_unauthenticated", "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_expired_token", "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_invalid_token", "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_profile", "tests/contract/test_users_me_get.py::TestUsersMeGet::test_get_current_user_unauthenticated", "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_empty_data", "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_invalid_token", "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_partial_data", "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_profile", "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_unauthenticated", "tests/contract/test_users_me_put.py::TestUsersMePut::test_update_current_user_username_conflict", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_deployment_status_changed", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_empty_payload", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_invalid_json", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_large_payload", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_missing_data", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_missing_event", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_task_completed", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_task_failed", "tests/contract/test_webhooks.py::TestWebhooks::test_webhook_unsupported_content_type"]