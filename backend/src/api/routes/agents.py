"""
Agent management routes for DahoAI platform
Agent marketplace endpoints
"""

from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from ...database import get_db
from ...models.user import User
from ...models.agent import PricingModel
from ...services.agent_service import agent_service
from ..middleware.auth import get_current_active_user, get_current_user_optional


router = APIRouter(prefix="/agents", tags=["Agents"])


# Pydantic models for request/response
class AgentResponse(BaseModel):
    id: str
    name: str
    description: str
    category: str
    version: str
    creator_id: str
    organization_id: str = None
    is_public: bool
    is_featured: bool
    is_active: bool
    pricing_model: str
    price: float = None
    currency: str
    tags: List[str] = []
    capabilities: dict
    requirements: dict = None
    download_count: int
    rating_average: float
    rating_count: int
    created_at: str
    updated_at: str


class AgentListResponse(BaseModel):
    agents: List[AgentResponse]
    total: int
    page: int
    limit: int


@router.get("", response_model=AgentListResponse)
async def get_agents(
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    featured: Optional[bool] = Query(None, description="Filter by featured status"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: Optional[User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get list of agents with filtering and pagination
    
    Args:
        category: Filter by category
        search: Search term for name and description
        featured: Filter by featured status
        page: Page number (1-based)
        limit: Items per page
        current_user: Current user (optional)
        db: Database session
        
    Returns:
        Paginated list of agents
    """
    skip = (page - 1) * limit
    
    agents = agent_service.get_agents(
        db=db,
        skip=skip,
        limit=limit,
        category=category,
        search=search,
        featured=featured,
        is_public=True  # Only show public agents in marketplace
    )
    
    # Convert to response format
    agent_responses = [
        AgentResponse(**agent.to_dict()) for agent in agents
    ]
    
    # For now, we'll use the returned count as total
    # In a real implementation, you'd want a separate count query
    total = len(agent_responses)
    
    return {
        "agents": agent_responses,
        "total": total,
        "page": page,
        "limit": limit
    }
