"""
User management routes for DahoAI platform
User profile endpoints
"""

from typing import Any, Optional

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from ...database import get_db
from ...models.user import User
from ...services.user_service import user_service
from ..middleware.auth import get_current_active_user


router = APIRouter(prefix="/users", tags=["Users"])


# Pydantic models for request/response
class UserResponse(BaseModel):
    id: str
    email: str
    username: str = None
    full_name: str = None
    avatar_url: str = None
    subscription_tier: str
    created_at: str
    is_active: bool


class UserUpdate(BaseModel):
    full_name: Optional[str] = Field(None, min_length=2, max_length=255)
    username: Optional[str] = Field(None, min_length=3, max_length=30)
    avatar_url: Optional[str] = Field(None, max_length=500)


@router.get("/me", response_model=UserResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Get current user profile
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Current user profile information
    """
    return current_user.to_dict()


@router.put("/me", response_model=UserResponse)
async def update_current_user_profile(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Update current user profile
    
    Args:
        user_update: User update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Updated user profile information
        
    Raises:
        HTTPException: If username already exists or update fails
    """
    # Filter out None values
    update_data = {k: v for k, v in user_update.dict().items() if v is not None}
    
    if not update_data:
        # No data to update, return current user
        return current_user.to_dict()
    
    try:
        updated_user = user_service.update_user(
            db=db,
            user_id=str(current_user.id),
            **update_data
        )
        
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        return updated_user.to_dict()
    
    except IntegrityError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )
