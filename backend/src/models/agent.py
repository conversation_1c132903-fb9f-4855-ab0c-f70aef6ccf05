"""
Agent model for DahoAI platform
"""

from sqlalchemy import Column, String, Boolean, DateTime, Enum, Integer, Numeric, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from ..database import Base


class PricingModel(enum.Enum):
    """Agent pricing models"""
    FREE = "FREE"
    PAID = "PAID"
    SUBSCRIPTION = "SUBSCRIPTION"


class Agent(Base):
    """
    Agent model representing AI agents available in the marketplace
    """
    __tablename__ = "agents"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Basic information
    name = Column(String(100), nullable=False)
    description = Column(String(2000), nullable=False)
    category = Column(String(50), nullable=False, index=True)
    version = Column(String(20), nullable=False)
    
    # Ownership
    creator_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=True)
    
    # Visibility and status
    is_public = Column(Boolean, default=True, nullable=False)
    is_featured = Column(Boolean, default=False, nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Pricing
    pricing_model = Column(Enum(PricingModel), nullable=False)
    price = Column(Numeric(10, 2), nullable=True)
    currency = Column(String(3), default="USD", nullable=False)
    
    # Metadata
    tags = Column(JSON, nullable=True)  # Array of strings
    capabilities = Column(JSON, nullable=False)  # Object with agent capabilities
    requirements = Column(JSON, nullable=True)  # Object with system requirements
    
    # Statistics
    download_count = Column(Integer, default=0, nullable=False)
    rating_average = Column(Numeric(3, 2), default=0.0, nullable=False, index=True)
    rating_count = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    creator = relationship("User", back_populates="created_agents")
    organization = relationship("Organization", back_populates="agents")
    versions = relationship("AgentVersion", back_populates="agent")
    reviews = relationship("Review", back_populates="agent")
    deployments = relationship("Deployment", back_populates="agent")
    tasks = relationship("Task", back_populates="assigned_agent")

    def __repr__(self):
        return f"<Agent(id={self.id}, name='{self.name}', creator_id={self.creator_id})>"

    def to_dict(self):
        """Convert agent to dictionary for API responses"""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "category": self.category,
            "version": self.version,
            "creator_id": str(self.creator_id),
            "organization_id": str(self.organization_id) if self.organization_id else None,
            "is_public": self.is_public,
            "is_featured": self.is_featured,
            "is_active": self.is_active,
            "pricing_model": self.pricing_model.value,
            "price": float(self.price) if self.price else None,
            "currency": self.currency,
            "tags": self.tags,
            "capabilities": self.capabilities,
            "requirements": self.requirements,
            "download_count": self.download_count,
            "rating_average": float(self.rating_average),
            "rating_count": self.rating_count,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
