"""
AgentVersion model for DahoAI platform
"""

from sqlalchemy import Column, String, <PERSON>olean, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from ..database import Base


class AgentVersion(Base):
    """
    AgentVersion model tracking different versions of agents
    """
    __tablename__ = "agent_versions"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Foreign key
    agent_id = Column(UUID(as_uuid=True), ForeignKey("agents.id"), nullable=False)
    
    # Version information
    version = Column(String(20), nullable=False)
    changelog = Column(Text, nullable=True)
    config_schema = Column(JSON, nullable=False)  # JSON schema for configuration
    
    # Status
    is_current = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    agent = relationship("Agent", back_populates="versions")

    def __repr__(self):
        return f"<AgentVersion(id={self.id}, agent_id={self.agent_id}, version='{self.version}')>"

    def to_dict(self):
        """Convert agent version to dictionary for API responses"""
        return {
            "id": str(self.id),
            "agent_id": str(self.agent_id),
            "version": self.version,
            "changelog": self.changelog,
            "config_schema": self.config_schema,
            "is_current": self.is_current,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
