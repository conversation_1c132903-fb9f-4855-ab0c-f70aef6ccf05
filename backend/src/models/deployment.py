"""
Deployment model for DahoAI platform
"""

from sqlalchemy import Column, String, DateTime, Enum, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from ..database import Base


class DeploymentStatus(enum.Enum):
    """Deployment status"""
    DEPLOYING = "DEPLOYING"
    RUNNING = "RUNNING"
    STOPPED = "STOPPED"
    FAILED = "FAILED"


class HealthStatus(enum.Enum):
    """Deployment health status"""
    HEALTHY = "HEALTHY"
    UNHEALTHY = "UNHEALTHY"
    UNKNOWN = "UNKNOWN"


class Deployment(Base):
    """
    Deployment model tracking agent deployments by users
    """
    __tablename__ = "deployments"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    agent_id = Column(UUID(as_uuid=True), ForeignKey("agents.id"), nullable=False, index=True)
    
    # Deployment information
    version = Column(String(20), nullable=False)
    status = Column(Enum(DeploymentStatus), nullable=False, index=True)
    config = Column(JSON, nullable=False)  # Deployment configuration
    endpoint_url = Column(String(500), nullable=True)
    
    # Health monitoring
    health_status = Column(Enum(HealthStatus), nullable=True)
    last_health_check = Column(DateTime, nullable=True)
    
    # Timestamps
    deployed_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    user = relationship("User", back_populates="deployments")
    agent = relationship("Agent", back_populates="deployments")

    def __repr__(self):
        return f"<Deployment(id={self.id}, user_id={self.user_id}, agent_id={self.agent_id}, status={self.status})>"

    def to_dict(self):
        """Convert deployment to dictionary for API responses"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "agent_id": str(self.agent_id),
            "version": self.version,
            "status": self.status.value,
            "config": self.config,
            "endpoint_url": self.endpoint_url,
            "health_status": self.health_status.value if self.health_status else None,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "deployed_at": self.deployed_at.isoformat() if self.deployed_at else None
        }
