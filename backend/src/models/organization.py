"""
Organization model for DahoAI platform
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from ..database import Base


class Organization(Base):
    """
    Organization model supporting team and enterprise accounts
    """
    __tablename__ = "organizations"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Basic information
    name = Column(String(100), nullable=False)
    description = Column(String(1000), nullable=True)
    
    # Owner relationship
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Settings
    is_active = Column(Boolean, default=True, nullable=False)
    max_members = Column(Integer, nullable=True)
    billing_email = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    # Owner of the organization
    owner = relationship("User", back_populates="owned_organizations")
    
    # Organization members
    members = relationship("OrganizationMember", back_populates="organization")
    
    # Agents shared with this organization
    agents = relationship("Agent", back_populates="organization")

    def __repr__(self):
        return f"<Organization(id={self.id}, name='{self.name}', owner_id={self.owner_id})>"

    def to_dict(self):
        """Convert organization to dictionary for API responses"""
        return {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "owner_id": str(self.owner_id),
            "is_active": self.is_active,
            "max_members": self.max_members,
            "billing_email": self.billing_email,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
