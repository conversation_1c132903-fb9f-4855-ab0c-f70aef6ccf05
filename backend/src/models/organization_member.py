"""
OrganizationMember model for DahoAI platform
"""

from sqlalchemy import Column, DateTime, Enum, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from ..database import Base


class OrganizationRole(enum.Enum):
    """Organization member roles"""
    OWNER = "OWNER"
    ADMIN = "ADMIN"
    MEMBER = "MEMBER"


class OrganizationMember(Base):
    """
    Junction table for organization membership with roles
    """
    __tablename__ = "organization_members"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Foreign keys
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    invited_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    # Role
    role = Column(Enum(OrganizationRole), default=OrganizationRole.MEMBER, nullable=False)
    
    # Timestamps
    joined_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    organization = relationship("Organization", back_populates="members")
    user = relationship("User", back_populates="organization_memberships", foreign_keys=[user_id])
    inviter = relationship("User", foreign_keys=[invited_by])

    # Constraints
    __table_args__ = (
        UniqueConstraint('organization_id', 'user_id', name='unique_org_user'),
    )

    def __repr__(self):
        return f"<OrganizationMember(id={self.id}, org_id={self.organization_id}, user_id={self.user_id}, role={self.role})>"

    def to_dict(self):
        """Convert organization member to dictionary for API responses"""
        return {
            "id": str(self.id),
            "organization_id": str(self.organization_id),
            "user_id": str(self.user_id),
            "role": self.role.value,
            "joined_at": self.joined_at.isoformat() if self.joined_at else None,
            "invited_by": str(self.invited_by) if self.invited_by else None
        }
