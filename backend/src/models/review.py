"""
Review model for DahoAI platform
"""

from sqlalchemy import <PERSON>umn, String, <PERSON>olean, DateTime, Integer, ForeignKey, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from ..database import Base


class Review(Base):
    """
    Review model for user reviews and ratings of agents
    """
    __tablename__ = "reviews"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Foreign keys
    agent_id = Column(UUID(as_uuid=True), ForeignKey("agents.id"), nullable=False, index=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Review content
    rating = Column(Integer, nullable=False)  # 1-5 stars
    title = Column(String(200), nullable=True)
    comment = Column(Text, nullable=True)
    
    # Status
    is_verified = Column(Boolean, default=False, nullable=False)  # User has purchased/used agent
    helpful_votes = Column(Integer, default=0, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    agent = relationship("Agent", back_populates="reviews")
    user = relationship("User", back_populates="reviews")

    # Constraints
    __table_args__ = (
        UniqueConstraint('agent_id', 'user_id', name='unique_agent_user_review'),
    )

    def __repr__(self):
        return f"<Review(id={self.id}, agent_id={self.agent_id}, user_id={self.user_id}, rating={self.rating})>"

    def to_dict(self):
        """Convert review to dictionary for API responses"""
        return {
            "id": str(self.id),
            "agent_id": str(self.agent_id),
            "user_id": str(self.user_id),
            "rating": self.rating,
            "title": self.title,
            "comment": self.comment,
            "is_verified": self.is_verified,
            "helpful_votes": self.helpful_votes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
