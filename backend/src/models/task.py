"""
Task model for DahoAI platform
"""

from sqlalchemy import Column, String, DateTime, Enum, Integer, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from ..database import Base


class TaskStatus(enum.Enum):
    """Task execution status"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class TaskPriority(enum.Enum):
    """Task priority levels"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    URGENT = "URGENT"


class Task(Base):
    """
    Task model representing user tasks assigned to agents
    """
    __tablename__ = "tasks"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Basic information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    
    # Ownership and assignment
    creator_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    assigned_agent_id = Column(UUID(as_uuid=True), ForeignKey("agents.id"), nullable=False, index=True)
    
    # Status and priority
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, nullable=False, index=True)
    priority = Column(Enum(TaskPriority), default=TaskPriority.MEDIUM, nullable=False)
    
    # Task data
    parameters = Column(JSON, nullable=True)  # Input parameters for the agent
    result = Column(JSON, nullable=True)  # Task execution result
    error_message = Column(Text, nullable=True)  # Error message if task failed
    
    # Timing
    estimated_duration = Column(Integer, nullable=True)  # Estimated duration in seconds
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)

    # Relationships
    creator = relationship("User", back_populates="created_tasks")
    assigned_agent = relationship("Agent", back_populates="tasks")
    logs = relationship("TaskLog", back_populates="task")

    def __repr__(self):
        return f"<Task(id={self.id}, title='{self.title}', status={self.status})>"

    def to_dict(self):
        """Convert task to dictionary for API responses"""
        return {
            "id": str(self.id),
            "title": self.title,
            "description": self.description,
            "creator_id": str(self.creator_id),
            "assigned_agent_id": str(self.assigned_agent_id),
            "status": self.status.value,
            "priority": self.priority.value,
            "parameters": self.parameters,
            "result": self.result,
            "error_message": self.error_message,
            "estimated_duration": self.estimated_duration,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }
