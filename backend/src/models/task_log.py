"""
TaskLog model for DahoAI platform
"""

from sqlalchemy import Column, DateTime, Enum, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from ..database import Base


class LogLevel(enum.Enum):
    """Task log levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class TaskLog(Base):
    """
    TaskLog model tracking execution logs for tasks
    """
    __tablename__ = "task_logs"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Foreign key
    task_id = Column(UUID(as_uuid=True), ForeignKey("tasks.id"), nullable=False, index=True)
    
    # Log information
    level = Column(Enum(LogLevel), nullable=False)
    message = Column(Text, nullable=False)
    log_metadata = Column(JSON, nullable=True)  # Additional structured data
    
    # Timestamp
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Relationships
    task = relationship("Task", back_populates="logs")

    def __repr__(self):
        return f"<TaskLog(id={self.id}, task_id={self.task_id}, level={self.level})>"

    def to_dict(self):
        """Convert task log to dictionary for API responses"""
        return {
            "id": str(self.id),
            "task_id": str(self.task_id),
            "level": self.level.value,
            "message": self.message,
            "metadata": self.log_metadata,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None
        }
