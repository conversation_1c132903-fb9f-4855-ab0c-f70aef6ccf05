"""
User model for DahoAI platform
"""

from sqlalchemy import Column, String, Boolean, DateTime, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import enum

from ..database import Base


class SubscriptionTier(enum.Enum):
    """User subscription tiers"""
    FREE = "FREE"
    PRO = "PRO"
    ENTERPRISE = "ENTERPRISE"


class User(Base):
    """
    User model representing platform users with authentication and profile information
    """
    __tablename__ = "users"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Authentication fields
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(30), unique=True, nullable=True, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Profile fields
    full_name = Column(String(255), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    
    # Account status
    email_verified = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Subscription
    subscription_tier = Column(
        Enum(SubscriptionTier), 
        default=SubscriptionTier.FREE, 
        nullable=False,
        index=True
    )
    subscription_expires_at = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login_at = Column(DateTime, nullable=True)

    # Relationships
    # Organizations where this user is the owner
    owned_organizations = relationship("Organization", back_populates="owner")
    
    # Organization memberships
    organization_memberships = relationship("OrganizationMember", back_populates="user")
    
    # Agents created by this user
    created_agents = relationship("Agent", back_populates="creator")
    
    # Tasks created by this user
    created_tasks = relationship("Task", back_populates="creator")
    
    # Reviews written by this user
    reviews = relationship("Review", back_populates="user")
    
    # Deployments by this user
    deployments = relationship("Deployment", back_populates="user")

    def __repr__(self):
        return f"<User(id={self.id}, email='{self.email}', username='{self.username}')>"

    def to_dict(self):
        """Convert user to dictionary for API responses"""
        return {
            "id": str(self.id),
            "email": self.email,
            "username": self.username,
            "full_name": self.full_name,
            "avatar_url": self.avatar_url,
            "subscription_tier": self.subscription_tier.value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "is_active": self.is_active
        }
