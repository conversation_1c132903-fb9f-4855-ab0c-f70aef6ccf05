"""
AgentService for DahoAI platform
Business logic for agent management operations
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime

from ..models.agent import Agent, PricingModel
from ..models.user import User
from ..database import get_db


class AgentService:
    """Service class for agent-related business logic"""
    
    def create_agent(
        self,
        db: Session,
        creator_id: str,
        name: str,
        description: str,
        category: str,
        version: str = "1.0.0",
        pricing_model: PricingModel = PricingModel.FREE,
        price: Optional[float] = None,
        currency: str = "USD",
        tags: Optional[List[str]] = None,
        capabilities: Optional[Dict[str, Any]] = None,
        requirements: Optional[Dict[str, Any]] = None,
        organization_id: Optional[str] = None
    ) -> Agent:
        """
        Create a new agent
        
        Args:
            db: Database session
            creator_id: ID of the user creating the agent
            name: Agent name
            description: Agent description
            category: Agent category
            version: Agent version (default: "1.0.0")
            pricing_model: Pricing model (default: FREE)
            price: Price if paid model
            currency: Currency code (default: "USD")
            tags: List of tags
            capabilities: Agent capabilities object
            requirements: System requirements object
            organization_id: Optional organization ID
            
        Returns:
            Created Agent object
        """
        agent = Agent(
            creator_id=creator_id,
            name=name,
            description=description,
            category=category,
            version=version,
            pricing_model=pricing_model,
            price=price,
            currency=currency,
            tags=tags or [],
            capabilities=capabilities or {},
            requirements=requirements or {},
            organization_id=organization_id
        )
        
        db.add(agent)
        db.commit()
        db.refresh(agent)
        return agent
    
    def get_agent_by_id(self, db: Session, agent_id: str) -> Optional[Agent]:
        """Get agent by ID"""
        return db.query(Agent).filter(Agent.id == agent_id).first()
    
    def get_agents(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 20,
        category: Optional[str] = None,
        search: Optional[str] = None,
        featured: Optional[bool] = None,
        creator_id: Optional[str] = None,
        is_public: bool = True
    ) -> List[Agent]:
        """
        Get list of agents with filtering and pagination
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            category: Filter by category
            search: Search in name and description
            featured: Filter by featured status
            creator_id: Filter by creator
            is_public: Filter by public visibility
            
        Returns:
            List of Agent objects
        """
        query = db.query(Agent).filter(Agent.is_active == True)
        
        if is_public:
            query = query.filter(Agent.is_public == True)
        
        if category:
            query = query.filter(Agent.category == category)
        
        if search:
            search_filter = or_(
                Agent.name.ilike(f"%{search}%"),
                Agent.description.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        if featured is not None:
            query = query.filter(Agent.is_featured == featured)
        
        if creator_id:
            query = query.filter(Agent.creator_id == creator_id)
        
        # Order by featured first, then by rating, then by creation date
        query = query.order_by(
            Agent.is_featured.desc(),
            Agent.rating_average.desc(),
            Agent.created_at.desc()
        )
        
        return query.offset(skip).limit(limit).all()
    
    def update_agent(
        self,
        db: Session,
        agent_id: str,
        creator_id: str,
        **kwargs
    ) -> Optional[Agent]:
        """
        Update agent information
        
        Args:
            db: Database session
            agent_id: Agent ID
            creator_id: ID of the user updating (must be creator)
            **kwargs: Fields to update
            
        Returns:
            Updated Agent object or None if not found/unauthorized
        """
        agent = db.query(Agent).filter(
            and_(Agent.id == agent_id, Agent.creator_id == creator_id)
        ).first()
        
        if not agent:
            return None
        
        # Update allowed fields
        allowed_fields = [
            'name', 'description', 'category', 'version', 'pricing_model',
            'price', 'currency', 'tags', 'capabilities', 'requirements',
            'is_public'
        ]
        
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(agent, field):
                setattr(agent, field, value)
        
        agent.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(agent)
        return agent
    
    def delete_agent(self, db: Session, agent_id: str, creator_id: str) -> bool:
        """
        Delete (deactivate) an agent
        
        Args:
            db: Database session
            agent_id: Agent ID
            creator_id: ID of the user deleting (must be creator)
            
        Returns:
            True if successful, False if not found/unauthorized
        """
        agent = db.query(Agent).filter(
            and_(Agent.id == agent_id, Agent.creator_id == creator_id)
        ).first()
        
        if not agent:
            return False
        
        agent.is_active = False
        agent.updated_at = datetime.utcnow()
        db.commit()
        return True
    
    def increment_download_count(self, db: Session, agent_id: str) -> bool:
        """
        Increment agent download count
        
        Args:
            db: Database session
            agent_id: Agent ID
            
        Returns:
            True if successful, False if agent not found
        """
        agent = self.get_agent_by_id(db, agent_id)
        if not agent:
            return False
        
        agent.download_count += 1
        db.commit()
        return True
    
    def update_rating(self, db: Session, agent_id: str, new_rating: float, rating_count: int) -> bool:
        """
        Update agent rating statistics
        
        Args:
            db: Database session
            agent_id: Agent ID
            new_rating: New average rating
            rating_count: New rating count
            
        Returns:
            True if successful, False if agent not found
        """
        agent = self.get_agent_by_id(db, agent_id)
        if not agent:
            return False
        
        agent.rating_average = new_rating
        agent.rating_count = rating_count
        db.commit()
        return True


# Global service instance
agent_service = AgentService()
