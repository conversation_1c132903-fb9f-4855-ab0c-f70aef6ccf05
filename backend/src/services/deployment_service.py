"""
DeploymentService for DahoAI platform
Business logic for deployment management operations
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_
from datetime import datetime

from ..models.deployment import Deployment, DeploymentStatus, HealthStatus
from ..database import get_db


class DeploymentService:
    """Service class for deployment-related business logic"""
    
    def create_deployment(
        self,
        db: Session,
        user_id: str,
        agent_id: str,
        version: str,
        config: Dict[str, Any]
    ) -> Deployment:
        """
        Create a new deployment
        
        Args:
            db: Database session
            user_id: ID of the user creating the deployment
            agent_id: ID of the agent to deploy
            version: Agent version to deploy
            config: Deployment configuration
            
        Returns:
            Created Deployment object
        """
        deployment = Deployment(
            user_id=user_id,
            agent_id=agent_id,
            version=version,
            status=DeploymentStatus.DEPLOYING,
            config=config,
            health_status=HealthStatus.UNKNOWN
        )
        
        db.add(deployment)
        db.commit()
        db.refresh(deployment)
        return deployment
    
    def get_deployment_by_id(self, db: Session, deployment_id: str) -> Optional[Deployment]:
        """Get deployment by ID"""
        return db.query(Deployment).filter(Deployment.id == deployment_id).first()
    
    def get_user_deployment_by_id(
        self, 
        db: Session, 
        deployment_id: str, 
        user_id: str
    ) -> Optional[Deployment]:
        """Get deployment by ID if it belongs to the user"""
        return db.query(Deployment).filter(
            and_(Deployment.id == deployment_id, Deployment.user_id == user_id)
        ).first()
    
    def get_deployments(
        self,
        db: Session,
        user_id: Optional[str] = None,
        agent_id: Optional[str] = None,
        status: Optional[DeploymentStatus] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[Deployment]:
        """
        Get list of deployments with filtering and pagination
        
        Args:
            db: Database session
            user_id: Filter by user
            agent_id: Filter by agent
            status: Filter by status
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of Deployment objects
        """
        query = db.query(Deployment)
        
        if user_id:
            query = query.filter(Deployment.user_id == user_id)
        
        if agent_id:
            query = query.filter(Deployment.agent_id == agent_id)
        
        if status:
            query = query.filter(Deployment.status == status)
        
        # Order by deployment date (newest first)
        query = query.order_by(Deployment.deployed_at.desc())
        
        return query.offset(skip).limit(limit).all()
    
    def update_deployment_status(
        self,
        db: Session,
        deployment_id: str,
        status: DeploymentStatus,
        endpoint_url: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Optional[Deployment]:
        """
        Update deployment status
        
        Args:
            db: Database session
            deployment_id: Deployment ID
            status: New status
            endpoint_url: Endpoint URL if deployment is running
            user_id: User ID (if provided, checks ownership)
            
        Returns:
            Updated Deployment object or None if not found/unauthorized
        """
        query = db.query(Deployment).filter(Deployment.id == deployment_id)
        
        if user_id:
            query = query.filter(Deployment.user_id == user_id)
        
        deployment = query.first()
        if not deployment:
            return None
        
        deployment.status = status
        
        if endpoint_url:
            deployment.endpoint_url = endpoint_url
        
        # Update health status based on deployment status
        if status == DeploymentStatus.RUNNING:
            deployment.health_status = HealthStatus.HEALTHY
        elif status in [DeploymentStatus.STOPPED, DeploymentStatus.FAILED]:
            deployment.health_status = HealthStatus.UNHEALTHY
        
        db.commit()
        db.refresh(deployment)
        return deployment
    
    def update_health_status(
        self,
        db: Session,
        deployment_id: str,
        health_status: HealthStatus
    ) -> Optional[Deployment]:
        """
        Update deployment health status
        
        Args:
            db: Database session
            deployment_id: Deployment ID
            health_status: New health status
            
        Returns:
            Updated Deployment object or None if not found
        """
        deployment = self.get_deployment_by_id(db, deployment_id)
        if not deployment:
            return None
        
        deployment.health_status = health_status
        deployment.last_health_check = datetime.utcnow()
        
        db.commit()
        db.refresh(deployment)
        return deployment
    
    def stop_deployment(
        self,
        db: Session,
        deployment_id: str,
        user_id: str
    ) -> Optional[Deployment]:
        """
        Stop a deployment
        
        Args:
            db: Database session
            deployment_id: Deployment ID
            user_id: User ID (must be owner)
            
        Returns:
            Updated Deployment object or None if not found/unauthorized
        """
        deployment = db.query(Deployment).filter(
            and_(
                Deployment.id == deployment_id,
                Deployment.user_id == user_id,
                Deployment.status.in_([DeploymentStatus.DEPLOYING, DeploymentStatus.RUNNING])
            )
        ).first()
        
        if not deployment:
            return None
        
        deployment.status = DeploymentStatus.STOPPED
        deployment.health_status = HealthStatus.UNHEALTHY
        deployment.last_health_check = datetime.utcnow()
        
        db.commit()
        db.refresh(deployment)
        return deployment
    
    def get_running_deployments(self, db: Session, user_id: str) -> List[Deployment]:
        """
        Get all running deployments for a user
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List of running Deployment objects
        """
        return db.query(Deployment).filter(
            and_(
                Deployment.user_id == user_id,
                Deployment.status == DeploymentStatus.RUNNING
            )
        ).all()
    
    def get_deployment_stats(self, db: Session, user_id: str) -> Dict[str, int]:
        """
        Get deployment statistics for a user
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            Dictionary with deployment counts by status
        """
        deployments = db.query(Deployment).filter(Deployment.user_id == user_id).all()
        
        stats = {
            "total": len(deployments),
            "deploying": 0,
            "running": 0,
            "stopped": 0,
            "failed": 0
        }
        
        for deployment in deployments:
            if deployment.status == DeploymentStatus.DEPLOYING:
                stats["deploying"] += 1
            elif deployment.status == DeploymentStatus.RUNNING:
                stats["running"] += 1
            elif deployment.status == DeploymentStatus.STOPPED:
                stats["stopped"] += 1
            elif deployment.status == DeploymentStatus.FAILED:
                stats["failed"] += 1
        
        return stats


# Global service instance
deployment_service = DeploymentService()
