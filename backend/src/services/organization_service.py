"""
OrganizationService for DahoAI platform
Business logic for organization management operations
"""

from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_
from datetime import datetime

from ..models.organization import Organization
from ..models.organization_member import OrganizationMember, OrganizationRole
from ..database import get_db


class OrganizationService:
    """Service class for organization-related business logic"""
    
    def create_organization(
        self,
        db: Session,
        owner_id: str,
        name: str,
        description: Optional[str] = None,
        max_members: Optional[int] = None,
        billing_email: Optional[str] = None
    ) -> Organization:
        """
        Create a new organization
        
        Args:
            db: Database session
            owner_id: ID of the user creating the organization
            name: Organization name
            description: Organization description
            max_members: Maximum number of members
            billing_email: Billing email address
            
        Returns:
            Created Organization object
        """
        organization = Organization(
            owner_id=owner_id,
            name=name,
            description=description,
            max_members=max_members,
            billing_email=billing_email
        )
        
        db.add(organization)
        db.commit()
        db.refresh(organization)
        
        # Add owner as a member with OWNER role
        self.add_member(
            db,
            organization.id,
            owner_id,
            OrganizationRole.OWNER,
            invited_by=owner_id
        )
        
        return organization
    
    def get_organization_by_id(self, db: Session, organization_id: str) -> Optional[Organization]:
        """Get organization by ID"""
        return db.query(Organization).filter(Organization.id == organization_id).first()
    
    def get_user_organizations(self, db: Session, user_id: str) -> List[Organization]:
        """
        Get all organizations where user is a member
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List of Organization objects
        """
        return db.query(Organization).join(OrganizationMember).filter(
            and_(
                OrganizationMember.user_id == user_id,
                Organization.is_active == True
            )
        ).all()
    
    def update_organization(
        self,
        db: Session,
        organization_id: str,
        user_id: str,
        **kwargs
    ) -> Optional[Organization]:
        """
        Update organization information
        
        Args:
            db: Database session
            organization_id: Organization ID
            user_id: User ID (must be owner or admin)
            **kwargs: Fields to update
            
        Returns:
            Updated Organization object or None if not found/unauthorized
        """
        # Check if user has permission to update
        member = db.query(OrganizationMember).filter(
            and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == user_id,
                OrganizationMember.role.in_([OrganizationRole.OWNER, OrganizationRole.ADMIN])
            )
        ).first()
        
        if not member:
            return None
        
        organization = self.get_organization_by_id(db, organization_id)
        if not organization:
            return None
        
        # Update allowed fields
        allowed_fields = ['name', 'description', 'max_members', 'billing_email']
        
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(organization, field):
                setattr(organization, field, value)
        
        organization.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(organization)
        return organization
    
    def add_member(
        self,
        db: Session,
        organization_id: str,
        user_id: str,
        role: OrganizationRole = OrganizationRole.MEMBER,
        invited_by: Optional[str] = None
    ) -> Optional[OrganizationMember]:
        """
        Add a member to an organization
        
        Args:
            db: Database session
            organization_id: Organization ID
            user_id: User ID to add
            role: Member role
            invited_by: ID of user who invited (optional)
            
        Returns:
            Created OrganizationMember object or None if already exists
        """
        # Check if member already exists
        existing_member = db.query(OrganizationMember).filter(
            and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == user_id
            )
        ).first()
        
        if existing_member:
            return None
        
        member = OrganizationMember(
            organization_id=organization_id,
            user_id=user_id,
            role=role,
            invited_by=invited_by
        )
        
        db.add(member)
        db.commit()
        db.refresh(member)
        return member
    
    def remove_member(
        self,
        db: Session,
        organization_id: str,
        user_id: str,
        removed_by: str
    ) -> bool:
        """
        Remove a member from an organization
        
        Args:
            db: Database session
            organization_id: Organization ID
            user_id: User ID to remove
            removed_by: ID of user performing the removal
            
        Returns:
            True if successful, False if not found/unauthorized
        """
        # Check if remover has permission
        remover_member = db.query(OrganizationMember).filter(
            and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == removed_by,
                OrganizationMember.role.in_([OrganizationRole.OWNER, OrganizationRole.ADMIN])
            )
        ).first()
        
        if not remover_member:
            return False
        
        # Find member to remove
        member_to_remove = db.query(OrganizationMember).filter(
            and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == user_id
            )
        ).first()
        
        if not member_to_remove:
            return False
        
        # Cannot remove the owner
        if member_to_remove.role == OrganizationRole.OWNER:
            return False
        
        db.delete(member_to_remove)
        db.commit()
        return True
    
    def update_member_role(
        self,
        db: Session,
        organization_id: str,
        user_id: str,
        new_role: OrganizationRole,
        updated_by: str
    ) -> Optional[OrganizationMember]:
        """
        Update a member's role
        
        Args:
            db: Database session
            organization_id: Organization ID
            user_id: User ID whose role to update
            new_role: New role
            updated_by: ID of user performing the update
            
        Returns:
            Updated OrganizationMember object or None if not found/unauthorized
        """
        # Check if updater has permission (only owner can change roles)
        updater_member = db.query(OrganizationMember).filter(
            and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == updated_by,
                OrganizationMember.role == OrganizationRole.OWNER
            )
        ).first()
        
        if not updater_member:
            return None
        
        # Find member to update
        member = db.query(OrganizationMember).filter(
            and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == user_id
            )
        ).first()
        
        if not member:
            return None
        
        # Cannot change owner role
        if member.role == OrganizationRole.OWNER or new_role == OrganizationRole.OWNER:
            return None
        
        member.role = new_role
        db.commit()
        db.refresh(member)
        return member
    
    def get_organization_members(
        self,
        db: Session,
        organization_id: str,
        role: Optional[OrganizationRole] = None
    ) -> List[OrganizationMember]:
        """
        Get organization members
        
        Args:
            db: Database session
            organization_id: Organization ID
            role: Filter by role (optional)
            
        Returns:
            List of OrganizationMember objects
        """
        query = db.query(OrganizationMember).filter(
            OrganizationMember.organization_id == organization_id
        )
        
        if role:
            query = query.filter(OrganizationMember.role == role)
        
        return query.order_by(OrganizationMember.joined_at).all()
    
    def is_member(self, db: Session, organization_id: str, user_id: str) -> bool:
        """
        Check if user is a member of the organization
        
        Args:
            db: Database session
            organization_id: Organization ID
            user_id: User ID
            
        Returns:
            True if user is a member, False otherwise
        """
        member = db.query(OrganizationMember).filter(
            and_(
                OrganizationMember.organization_id == organization_id,
                OrganizationMember.user_id == user_id
            )
        ).first()
        
        return member is not None


# Global service instance
organization_service = OrganizationService()
