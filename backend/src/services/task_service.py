"""
TaskService for DahoAI platform
Business logic for task management operations
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_
from datetime import datetime

from ..models.task import Task, TaskStatus, TaskPriority
from ..models.task_log import TaskLog, LogLevel
from ..database import get_db


class TaskService:
    """Service class for task-related business logic"""
    
    def create_task(
        self,
        db: Session,
        creator_id: str,
        title: str,
        description: str,
        assigned_agent_id: str,
        priority: TaskPriority = TaskPriority.MEDIUM,
        parameters: Optional[Dict[str, Any]] = None,
        estimated_duration: Optional[int] = None
    ) -> Task:
        """
        Create a new task
        
        Args:
            db: Database session
            creator_id: ID of the user creating the task
            title: Task title
            description: Task description
            assigned_agent_id: ID of the agent to execute the task
            priority: Task priority (default: MEDIUM)
            parameters: Task parameters for the agent
            estimated_duration: Estimated duration in seconds
            
        Returns:
            Created Task object
        """
        task = Task(
            creator_id=creator_id,
            title=title,
            description=description,
            assigned_agent_id=assigned_agent_id,
            priority=priority,
            parameters=parameters or {},
            estimated_duration=estimated_duration
        )
        
        db.add(task)
        db.commit()
        db.refresh(task)
        
        # Log task creation
        self.add_task_log(
            db, 
            task.id, 
            LogLevel.INFO, 
            f"Task '{title}' created and assigned to agent {assigned_agent_id}"
        )
        
        return task
    
    def get_task_by_id(self, db: Session, task_id: str) -> Optional[Task]:
        """Get task by ID"""
        return db.query(Task).filter(Task.id == task_id).first()
    
    def get_user_task_by_id(self, db: Session, task_id: str, user_id: str) -> Optional[Task]:
        """Get task by ID if it belongs to the user"""
        return db.query(Task).filter(
            and_(Task.id == task_id, Task.creator_id == user_id)
        ).first()
    
    def get_tasks(
        self,
        db: Session,
        creator_id: Optional[str] = None,
        assigned_agent_id: Optional[str] = None,
        status: Optional[TaskStatus] = None,
        priority: Optional[TaskPriority] = None,
        skip: int = 0,
        limit: int = 20
    ) -> List[Task]:
        """
        Get list of tasks with filtering and pagination
        
        Args:
            db: Database session
            creator_id: Filter by creator
            assigned_agent_id: Filter by assigned agent
            status: Filter by status
            priority: Filter by priority
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of Task objects
        """
        query = db.query(Task)
        
        if creator_id:
            query = query.filter(Task.creator_id == creator_id)
        
        if assigned_agent_id:
            query = query.filter(Task.assigned_agent_id == assigned_agent_id)
        
        if status:
            query = query.filter(Task.status == status)
        
        if priority:
            query = query.filter(Task.priority == priority)
        
        # Order by priority (URGENT first), then by creation date
        priority_order = {
            TaskPriority.URGENT: 4,
            TaskPriority.HIGH: 3,
            TaskPriority.MEDIUM: 2,
            TaskPriority.LOW: 1
        }
        
        query = query.order_by(Task.created_at.desc())
        
        return query.offset(skip).limit(limit).all()
    
    def update_task_status(
        self,
        db: Session,
        task_id: str,
        status: TaskStatus,
        user_id: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> Optional[Task]:
        """
        Update task status
        
        Args:
            db: Database session
            task_id: Task ID
            status: New status
            user_id: User ID (if provided, checks ownership)
            result: Task result if completed
            error_message: Error message if failed
            
        Returns:
            Updated Task object or None if not found/unauthorized
        """
        query = db.query(Task).filter(Task.id == task_id)
        
        if user_id:
            query = query.filter(Task.creator_id == user_id)
        
        task = query.first()
        if not task:
            return None
        
        old_status = task.status
        task.status = status
        task.updated_at = datetime.utcnow()
        
        # Update timestamps based on status
        if status == TaskStatus.RUNNING and old_status == TaskStatus.PENDING:
            task.started_at = datetime.utcnow()
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            task.completed_at = datetime.utcnow()
        
        # Update result or error message
        if result:
            task.result = result
            task.error_message = None
        elif error_message:
            task.error_message = error_message
            task.result = None
        
        db.commit()
        db.refresh(task)
        
        # Log status change
        self.add_task_log(
            db,
            task_id,
            LogLevel.INFO,
            f"Task status changed from {old_status.value} to {status.value}"
        )
        
        return task
    
    def update_task(
        self,
        db: Session,
        task_id: str,
        user_id: str,
        **kwargs
    ) -> Optional[Task]:
        """
        Update task information
        
        Args:
            db: Database session
            task_id: Task ID
            user_id: User ID (must be creator)
            **kwargs: Fields to update
            
        Returns:
            Updated Task object or None if not found/unauthorized
        """
        task = db.query(Task).filter(
            and_(Task.id == task_id, Task.creator_id == user_id)
        ).first()
        
        if not task:
            return None
        
        # Update allowed fields
        allowed_fields = ['title', 'description', 'priority', 'parameters']
        
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(task, field):
                setattr(task, field, value)
        
        task.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(task)
        return task
    
    def add_task_log(
        self,
        db: Session,
        task_id: str,
        level: LogLevel,
        message: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> TaskLog:
        """
        Add a log entry to a task
        
        Args:
            db: Database session
            task_id: Task ID
            level: Log level
            message: Log message
            metadata: Additional metadata
            
        Returns:
            Created TaskLog object
        """
        log = TaskLog(
            task_id=task_id,
            level=level,
            message=message,
            log_metadata=metadata
        )
        
        db.add(log)
        db.commit()
        db.refresh(log)
        return log
    
    def get_task_logs(
        self,
        db: Session,
        task_id: str,
        level: Optional[LogLevel] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[TaskLog]:
        """
        Get task logs
        
        Args:
            db: Database session
            task_id: Task ID
            level: Filter by log level
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of TaskLog objects
        """
        query = db.query(TaskLog).filter(TaskLog.task_id == task_id)
        
        if level:
            query = query.filter(TaskLog.level == level)
        
        query = query.order_by(TaskLog.timestamp.desc())
        
        return query.offset(skip).limit(limit).all()


# Global service instance
task_service = TaskService()
