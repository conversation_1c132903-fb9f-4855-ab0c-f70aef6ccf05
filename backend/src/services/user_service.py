"""
UserService for DahoAI platform
Business logic for user management operations
"""

from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime

from ..models.user import User, SubscriptionTier
from ..database import get_db


class UserService:
    """Service class for user-related business logic"""
    
    def __init__(self):
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def create_user(
        self, 
        db: Session, 
        email: str, 
        password: str, 
        full_name: str,
        username: Optional[str] = None
    ) -> User:
        """
        Create a new user
        
        Args:
            db: Database session
            email: User email
            password: Plain text password
            full_name: User's full name
            username: Optional username
            
        Returns:
            Created User object
            
        Raises:
            IntegrityError: If email or username already exists
        """
        hashed_password = self.hash_password(password)
        
        user = User(
            email=email,
            password_hash=hashed_password,
            full_name=full_name,
            username=username,
            subscription_tier=SubscriptionTier.FREE
        )
        
        try:
            db.add(user)
            db.commit()
            db.refresh(user)
            return user
        except IntegrityError:
            db.rollback()
            raise
    
    def get_user_by_id(self, db: Session, user_id: str) -> Optional[User]:
        """Get user by ID"""
        return db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_email(self, db: Session, email: str) -> Optional[User]:
        """Get user by email"""
        return db.query(User).filter(User.email == email).first()
    
    def get_user_by_username(self, db: Session, username: str) -> Optional[User]:
        """Get user by username"""
        return db.query(User).filter(User.username == username).first()
    
    def authenticate_user(self, db: Session, email: str, password: str) -> Optional[User]:
        """
        Authenticate user with email and password
        
        Args:
            db: Database session
            email: User email
            password: Plain text password
            
        Returns:
            User object if authentication successful, None otherwise
        """
        user = self.get_user_by_email(db, email)
        if not user:
            return None
        
        if not self.verify_password(password, user.password_hash):
            return None
        
        # Update last login timestamp
        user.last_login_at = datetime.utcnow()
        db.commit()
        
        return user
    
    def update_user(
        self, 
        db: Session, 
        user_id: str, 
        **kwargs
    ) -> Optional[User]:
        """
        Update user information
        
        Args:
            db: Database session
            user_id: User ID
            **kwargs: Fields to update
            
        Returns:
            Updated User object or None if not found
        """
        user = self.get_user_by_id(db, user_id)
        if not user:
            return None
        
        # Update allowed fields
        allowed_fields = ['full_name', 'username', 'avatar_url']
        for field, value in kwargs.items():
            if field in allowed_fields and hasattr(user, field):
                setattr(user, field, value)
        
        user.updated_at = datetime.utcnow()
        
        try:
            db.commit()
            db.refresh(user)
            return user
        except IntegrityError:
            db.rollback()
            raise
    
    def deactivate_user(self, db: Session, user_id: str) -> bool:
        """
        Deactivate a user account
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            True if successful, False if user not found
        """
        user = self.get_user_by_id(db, user_id)
        if not user:
            return False
        
        user.is_active = False
        user.updated_at = datetime.utcnow()
        db.commit()
        return True
    
    def get_users(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 20,
        subscription_tier: Optional[SubscriptionTier] = None
    ) -> List[User]:
        """
        Get list of users with pagination and filtering
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            subscription_tier: Filter by subscription tier
            
        Returns:
            List of User objects
        """
        query = db.query(User)
        
        if subscription_tier:
            query = query.filter(User.subscription_tier == subscription_tier)
        
        return query.offset(skip).limit(limit).all()


# Global service instance
user_service = UserService()
