import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestAgentsCreate:
    """Contract tests for POST /agents endpoint."""

    @pytest.mark.asyncio
    async def test_create_agent_successful(self, client: AsyncClient, auth_headers):
        """Test creating a new agent with valid data."""
        agent_data = {
            "name": "Test Agent",
            "description": "A test agent for demonstration",
            "category": "productivity",
            "version": "1.0.0",
            "pricing_model": "FREE",
            "price": 0,
            "currency": "USD",
            "tags": ["test", "demo"],
            "capabilities": {
                "input_types": ["text"],
                "output_types": ["text"],
                "max_tokens": 1000
            },
            "requirements": {
                "min_memory": "512MB",
                "supported_platforms": ["linux", "macos"]
            }
        }

        response = await client.post("/agents", json=agent_data, headers=auth_headers)

        # Should return 201 with created agent
        assert response.status_code == 201
        agent = response.json()

        # Validate created agent structure
        assert "id" in agent
        assert agent["name"] == agent_data["name"]
        assert agent["description"] == agent_data["description"]
        assert agent["category"] == agent_data["category"]
        assert agent["version"] == agent_data["version"]
        assert "creator" in agent
        assert agent["pricing_model"] == agent_data["pricing_model"]
        assert agent["price"] == agent_data["price"]
        assert agent["currency"] == agent_data["currency"]
        assert agent["tags"] == agent_data["tags"]
        assert agent["capabilities"] == agent_data["capabilities"]
        assert agent["requirements"] == agent_data["requirements"]

    @pytest.mark.asyncio
    async def test_create_agent_paid_model(self, client: AsyncClient, auth_headers):
        """Test creating a paid agent."""
        agent_data = {
            "name": "Premium Agent",
            "description": "A premium agent",
            "category": "business",
            "version": "2.0.0",
            "pricing_model": "SUBSCRIPTION",
            "price": 29.99,
            "currency": "USD",
            "tags": ["premium", "business"],
            "capabilities": {
                "input_types": ["text", "file"],
                "output_types": ["text", "json"],
                "max_tokens": 5000
            },
            "requirements": {
                "min_memory": "1GB",
                "supported_platforms": ["linux", "macos", "windows"]
            }
        }

        response = await client.post("/agents", json=agent_data, headers=auth_headers)

        assert response.status_code == 201
        agent = response.json()
        assert agent["pricing_model"] == "SUBSCRIPTION"
        assert agent["price"] == 29.99

    @pytest.mark.asyncio
    async def test_create_agent_missing_required_fields(self, client: AsyncClient, auth_headers):
        """Test creating agent with missing required fields."""
        incomplete_data = {
            "name": "Incomplete Agent"
            # Missing description, category, etc.
        }

        response = await client.post("/agents", json=incomplete_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_agent_invalid_pricing_model(self, client: AsyncClient, auth_headers):
        """Test creating agent with invalid pricing model."""
        invalid_data = {
            "name": "Test Agent",
            "description": "A test agent",
            "category": "productivity",
            "version": "1.0.0",
            "pricing_model": "INVALID_MODEL",  # Invalid
            "price": 0,
            "currency": "USD",
            "tags": ["test"],
            "capabilities": {},
            "requirements": {}
        }

        response = await client.post("/agents", json=invalid_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_agent_unauthenticated(self, client: AsyncClient):
        """Test creating agent without authentication."""
        agent_data = {
            "name": "Test Agent",
            "description": "A test agent",
            "category": "productivity",
            "version": "1.0.0",
            "pricing_model": "FREE",
            "price": 0,
            "currency": "USD",
            "tags": ["test"],
            "capabilities": {},
            "requirements": {}
        }

        response = await client.post("/agents", json=agent_data)

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_create_agent_duplicate_name(self, client: AsyncClient, auth_headers):
        """Test creating agent with name that already exists."""
        agent_data = {
            "name": "Duplicate Agent",
            "description": "First agent",
            "category": "productivity",
            "version": "1.0.0",
            "pricing_model": "FREE",
            "price": 0,
            "currency": "USD",
            "tags": ["test"],
            "capabilities": {},
            "requirements": {}
        }

        # Create first agent
        response1 = await client.post("/agents", json=agent_data, headers=auth_headers)
        assert response1.status_code == 201

        # Try to create second agent with same name
        agent_data["description"] = "Second agent"
        response2 = await client.post("/agents", json=agent_data, headers=auth_headers)

        # Should return 400 for duplicate name
        assert response2.status_code == 400