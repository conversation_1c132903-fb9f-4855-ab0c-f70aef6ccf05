import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestAgentsGet:
    """Contract tests for GET /agents/{agent_id} endpoint."""

    @pytest.mark.asyncio
    async def test_get_agent_by_id(self, client: AsyncClient):
        """Test getting agent details by ID."""
        # First create an agent to retrieve
        agent_data = {
            "name": "Test Agent",
            "description": "A test agent for retrieval",
            "category": "productivity",
            "version": "1.0.0",
            "pricing_model": "FREE",
            "price": 0,
            "currency": "USD",
            "tags": ["test"],
            "capabilities": {
                "input_types": ["text"],
                "output_types": ["text"]
            },
            "requirements": {
                "min_memory": "512MB"
            }
        }

        # This would normally be done with auth headers, but for contract testing
        # we'll assume the agent exists
        agent_id = str(uuid4())

        response = await client.get(f"/agents/{agent_id}")

        # Should return 200 with agent details
        assert response.status_code == 200
        agent = response.json()

        # Validate agent structure
        assert "id" in agent
        assert "name" in agent
        assert "description" in agent
        assert "category" in agent
        assert "version" in agent
        assert "creator" in agent
        assert "pricing_model" in agent
        assert "price" in agent
        assert "currency" in agent
        assert "tags" in agent
        assert "capabilities" in agent
        assert "requirements" in agent

    @pytest.mark.asyncio
    async def test_get_agent_not_found(self, client: AsyncClient):
        """Test getting agent that doesn't exist."""
        non_existent_id = str(uuid4())

        response = await client.get(f"/agents/{non_existent_id}")

        # Should return 404 for non-existent agent
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_agent_invalid_uuid(self, client: AsyncClient):
        """Test getting agent with invalid UUID format."""
        invalid_id = "not-a-uuid"

        response = await client.get(f"/agents/{invalid_id}")

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_agent_empty_uuid(self, client: AsyncClient):
        """Test getting agent with empty UUID."""
        response = await client.get("/agents/")

        # Should return 404 or 405 depending on routing
        assert response.status_code in [404, 405]

    @pytest.mark.asyncio
    async def test_get_agent_malformed_uuid(self, client: AsyncClient):
        """Test getting agent with malformed UUID."""
        malformed_id = "123e4567-e89b-12d3-a456-***********"  # Missing last character

        response = await client.get(f"/agents/{malformed_id}")

        # Should return 422 for validation error
        assert response.status_code == 422