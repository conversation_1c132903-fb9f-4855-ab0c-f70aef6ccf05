import pytest
from httpx import AsyncClient


class TestAgentsList:
    """Contract tests for GET /agents endpoint."""

    @pytest.mark.asyncio
    async def test_get_agents_list(self, client: AsyncClient):
        """Test getting list of agents without filters."""
        response = await client.get("/agents")

        # Should return 200 with agents list
        assert response.status_code == 200
        data = response.json()

        # Validate response structure
        assert "agents" in data
        assert "total" in data
        assert "page" in data
        assert "limit" in data
        assert isinstance(data["agents"], list)
        assert isinstance(data["total"], int)
        assert isinstance(data["page"], int)
        assert isinstance(data["limit"], int)

    @pytest.mark.asyncio
    async def test_get_agents_with_category_filter(self, client: AsyncClient):
        """Test getting agents filtered by category."""
        response = await client.get("/agents?category=productivity")

        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert isinstance(data["agents"], list)

        # If there are agents, validate their structure
        if data["agents"]:
            agent = data["agents"][0]
            assert "id" in agent
            assert "name" in agent
            assert "description" in agent
            assert "category" in agent
            assert "version" in agent
            assert "creator" in agent
            assert "pricing_model" in agent
            assert "price" in agent
            assert "currency" in agent
            assert "tags" in agent
            assert "capabilities" in agent
            assert "requirements" in agent

    @pytest.mark.asyncio
    async def test_get_agents_with_search_filter(self, client: AsyncClient):
        """Test getting agents filtered by search query."""
        response = await client.get("/agents?search=productivity")

        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert isinstance(data["agents"], list)

    @pytest.mark.asyncio
    async def test_get_agents_featured_only(self, client: AsyncClient):
        """Test getting only featured agents."""
        response = await client.get("/agents?featured=true")

        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert isinstance(data["agents"], list)

    @pytest.mark.asyncio
    async def test_get_agents_pagination(self, client: AsyncClient):
        """Test agents list pagination."""
        response = await client.get("/agents?page=1&limit=10")

        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert "page" in data
        assert "limit" in data
        assert data["page"] == 1
        assert data["limit"] == 10
        assert len(data["agents"]) <= 10

    @pytest.mark.asyncio
    async def test_get_agents_invalid_page(self, client: AsyncClient):
        """Test agents list with invalid page number."""
        response = await client.get("/agents?page=0")

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_agents_invalid_limit(self, client: AsyncClient):
        """Test agents list with invalid limit."""
        response = await client.get("/agents?limit=150")  # Over maximum

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_agents_empty_result(self, client: AsyncClient):
        """Test agents list when no agents match filters."""
        response = await client.get("/agents?category=nonexistent")

        assert response.status_code == 200
        data = response.json()
        assert "agents" in data
        assert data["agents"] == []
        assert data["total"] == 0