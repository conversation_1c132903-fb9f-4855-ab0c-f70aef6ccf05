import pytest
from httpx import AsyncClient


class TestAuthLogin:
    """Contract tests for POST /auth/login endpoint."""

    @pytest.mark.asyncio
    async def test_login_successful(self, client: AsyncClient):
        """Test successful user login with valid credentials."""
        login_data = {
            "email": "<EMAIL>",
            "password": "validpassword123"
        }

        response = await client.post("/auth/login", json=login_data)

        # This should return 200 with access token and user data
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
        assert "expires_in" in data
        assert "user" in data

        # Validate user object structure
        user = data["user"]
        assert "id" in user
        assert "email" in user
        assert user["email"] == login_data["email"]
        assert "username" in user
        assert "full_name" in user
        assert "subscription_tier" in user
        assert "created_at" in user
        assert "is_active" in user

    @pytest.mark.asyncio
    async def test_login_invalid_credentials(self, client: AsyncClient):
        """Test login with invalid credentials."""
        login_data = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }

        response = await client.post("/auth/login", json=login_data)

        # Should return 401 for invalid credentials
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_login_missing_email(self, client: AsyncClient):
        """Test login with missing email field."""
        login_data = {
            "password": "validpassword123"
        }

        response = await client.post("/auth/login", json=login_data)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_login_missing_password(self, client: AsyncClient):
        """Test login with missing password field."""
        login_data = {
            "email": "<EMAIL>"
        }

        response = await client.post("/auth/login", json=login_data)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_login_invalid_email_format(self, client: AsyncClient):
        """Test login with invalid email format."""
        login_data = {
            "email": "invalid-email",
            "password": "validpassword123"
        }

        response = await client.post("/auth/login", json=login_data)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_login_short_password(self, client: AsyncClient):
        """Test login with password shorter than minimum length."""
        login_data = {
            "email": "<EMAIL>",
            "password": "short"
        }

        response = await client.post("/auth/login", json=login_data)

        # Should return 422 for validation error
        assert response.status_code == 422