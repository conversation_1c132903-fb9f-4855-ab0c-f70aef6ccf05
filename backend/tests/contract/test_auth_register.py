import pytest
from httpx import AsyncClient


class TestAuthRegister:
    """Contract tests for POST /auth/register endpoint."""

    @pytest.mark.asyncio
    async def test_register_successful(self, client: AsyncClient):
        """Test successful user registration."""
        register_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "<PERSON>",
            "username": "johndo<PERSON>"
        }

        response = await client.post("/auth/register", json=register_data)

        # Should return 201 with user data
        assert response.status_code == 201
        user = response.json()

        # Validate user object structure
        assert "id" in user
        assert "email" in user
        assert user["email"] == register_data["email"]
        assert "username" in user
        assert user["username"] == register_data["username"]
        assert "full_name" in user
        assert user["full_name"] == register_data["full_name"]
        assert "subscription_tier" in user
        assert "created_at" in user
        assert "is_active" in user

    @pytest.mark.asyncio
    async def test_register_duplicate_email(self, client: AsyncClient):
        """Test registration with email that already exists."""
        register_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "<PERSON>",
            "username": "janedoe"
        }

        # First registration
        response1 = await client.post("/auth/register", json=register_data)
        assert response1.status_code == 201

        # Second registration with same email
        response2 = await client.post("/auth/register", json=register_data)
        assert response2.status_code == 400

    @pytest.mark.asyncio
    async def test_register_duplicate_username(self, client: AsyncClient):
        """Test registration with username that already exists."""
        register_data1 = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "User One",
            "username": "testuser"
        }

        register_data2 = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "User Two",
            "username": "testuser"  # Same username
        }

        # First registration
        response1 = await client.post("/auth/register", json=register_data1)
        assert response1.status_code == 201

        # Second registration with same username
        response2 = await client.post("/auth/register", json=register_data2)
        assert response2.status_code == 400

    @pytest.mark.asyncio
    async def test_register_missing_required_fields(self, client: AsyncClient):
        """Test registration with missing required fields."""
        # Missing email
        register_data = {
            "password": "securepassword123",
            "full_name": "John Doe"
        }

        response = await client.post("/auth/register", json=register_data)
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_register_invalid_email(self, client: AsyncClient):
        """Test registration with invalid email format."""
        register_data = {
            "email": "invalid-email",
            "password": "securepassword123",
            "full_name": "John Doe",
            "username": "johndoe"
        }

        response = await client.post("/auth/register", json=register_data)
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_register_short_password(self, client: AsyncClient):
        """Test registration with password shorter than minimum length."""
        register_data = {
            "email": "<EMAIL>",
            "password": "short",
            "full_name": "John Doe",
            "username": "johndoe"
        }

        response = await client.post("/auth/register", json=register_data)
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_register_short_username(self, client: AsyncClient):
        """Test registration with username shorter than minimum length."""
        register_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "John Doe",
            "username": "ab"  # Too short
        }

        response = await client.post("/auth/register", json=register_data)
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_register_short_full_name(self, client: AsyncClient):
        """Test registration with full name shorter than minimum length."""
        register_data = {
            "email": "<EMAIL>",
            "password": "securepassword123",
            "full_name": "A",  # Too short
            "username": "johndoe"
        }

        response = await client.post("/auth/register", json=register_data)
        assert response.status_code == 422