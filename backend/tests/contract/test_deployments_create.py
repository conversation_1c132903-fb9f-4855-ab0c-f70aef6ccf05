import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestDeploymentsCreate:
    """Contract tests for POST /deployments endpoint."""

    @pytest.mark.asyncio
    async def test_create_deployment_successful(self, client: AsyncClient, auth_headers):
        """Test creating a new deployment with valid data."""
        agent_id = str(uuid4())
        deployment_data = {
            "agent_id": agent_id,
            "config": {
                "environment": "production",
                "replicas": 1,
                "resources": {
                    "cpu": "500m",
                    "memory": "512Mi"
                }
            }
        }

        response = await client.post("/deployments", json=deployment_data, headers=auth_headers)

        # Should return 201 with deployment details
        assert response.status_code == 201
        deployment = response.json()

        # Validate deployment structure
        assert "id" in deployment
        assert deployment["agent_id"] == agent_id
        assert "status" in deployment
        assert deployment["config"] == deployment_data["config"]
        assert "created_at" in deployment
        assert "updated_at" in deployment

    @pytest.mark.asyncio
    async def test_create_deployment_minimal_config(self, client: AsyncClient, auth_headers):
        """Test creating a deployment with minimal configuration."""
        agent_id = str(uuid4())
        deployment_data = {
            "agent_id": agent_id
            # No config provided - should use defaults
        }

        response = await client.post("/deployments", json=deployment_data, headers=auth_headers)

        assert response.status_code == 201
        deployment = response.json()
        assert deployment["agent_id"] == agent_id
        assert "config" in deployment

    @pytest.mark.asyncio
    async def test_create_deployment_missing_agent_id(self, client: AsyncClient, auth_headers):
        """Test creating deployment with missing agent_id."""
        deployment_data = {
            "config": {
                "environment": "test"
            }
        }

        response = await client.post("/deployments", json=deployment_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_deployment_invalid_agent_id(self, client: AsyncClient, auth_headers):
        """Test creating deployment with invalid agent_id format."""
        deployment_data = {
            "agent_id": "not-a-uuid",
            "config": {}
        }

        response = await client.post("/deployments", json=deployment_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_deployment_nonexistent_agent(self, client: AsyncClient, auth_headers):
        """Test creating deployment for agent that doesn't exist."""
        non_existent_agent_id = str(uuid4())
        deployment_data = {
            "agent_id": non_existent_agent_id,
            "config": {}
        }

        response = await client.post("/deployments", json=deployment_data, headers=auth_headers)

        # Should return 400 for invalid agent
        assert response.status_code == 400

    @pytest.mark.asyncio
    async def test_create_deployment_unauthenticated(self, client: AsyncClient):
        """Test creating deployment without authentication."""
        agent_id = str(uuid4())
        deployment_data = {
            "agent_id": agent_id,
            "config": {}
        }

        response = await client.post("/deployments", json=deployment_data)

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_create_deployment_invalid_config(self, client: AsyncClient, auth_headers):
        """Test creating deployment with invalid configuration."""
        agent_id = str(uuid4())
        deployment_data = {
            "agent_id": agent_id,
            "config": {
                "invalid_field": "invalid_value",
                "replicas": -1  # Invalid negative value
            }
        }

        response = await client.post("/deployments", json=deployment_data, headers=auth_headers)

        # Should return 400 for invalid configuration
        assert response.status_code == 400