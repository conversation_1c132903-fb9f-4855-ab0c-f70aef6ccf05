import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestDeploymentsDelete:
    """Contract tests for DELETE /deployments/{deployment_id} endpoint."""

    @pytest.mark.asyncio
    async def test_delete_deployment_successful(self, client: AsyncClient, auth_headers):
        """Test successfully deleting a deployment."""
        # Assume a deployment exists for this user
        deployment_id = str(uuid4())

        response = await client.delete(f"/deployments/{deployment_id}", headers=auth_headers)

        # Should return 204 for successful deletion
        assert response.status_code == 204

    @pytest.mark.asyncio
    async def test_delete_deployment_not_found(self, client: AsyncClient, auth_headers):
        """Test deleting deployment that doesn't exist."""
        non_existent_id = str(uuid4())

        response = await client.delete(f"/deployments/{non_existent_id}", headers=auth_headers)

        # Should return 404 for non-existent deployment
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_deployment_not_owned_by_user(self, client: AsyncClient, auth_headers):
        """Test deleting deployment owned by another user."""
        other_user_deployment_id = str(uuid4())

        response = await client.delete(f"/deployments/{other_user_deployment_id}", headers=auth_headers)

        # Should return 404 (not found for this user)
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_delete_deployment_invalid_uuid(self, client: AsyncClient, auth_headers):
        """Test deleting deployment with invalid UUID format."""
        invalid_id = "not-a-uuid"

        response = await client.delete(f"/deployments/{invalid_id}", headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_delete_deployment_unauthenticated(self, client: AsyncClient):
        """Test deleting deployment without authentication."""
        deployment_id = str(uuid4())

        response = await client.delete(f"/deployments/{deployment_id}")

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_delete_deployment_invalid_token(self, client: AsyncClient):
        """Test deleting deployment with invalid token."""
        deployment_id = str(uuid4())
        invalid_headers = {"Authorization": "Bearer invalid-token"}

        response = await client.delete(f"/deployments/{deployment_id}", headers=invalid_headers)

        # Should return 401 for invalid token
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_delete_deployment_already_stopped(self, client: AsyncClient, auth_headers):
        """Test deleting deployment that is already stopped."""
        deployment_id = str(uuid4())

        # First delete
        response1 = await client.delete(f"/deployments/{deployment_id}", headers=auth_headers)
        assert response1.status_code == 204

        # Try to delete again
        response2 = await client.delete(f"/deployments/{deployment_id}", headers=auth_headers)

        # Should return 404 (already deleted/not found)
        assert response2.status_code == 404