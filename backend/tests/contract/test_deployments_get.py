import pytest
from httpx import As<PERSON>Client
from uuid import uuid4


class TestDeploymentsGet:
    """Contract tests for GET /deployments/{deployment_id} endpoint."""

    @pytest.mark.asyncio
    async def test_get_deployment_by_id(self, client: AsyncClient, auth_headers):
        """Test getting deployment details by ID."""
        # Assume a deployment exists for this user
        deployment_id = str(uuid4())

        response = await client.get(f"/deployments/{deployment_id}", headers=auth_headers)

        # Should return 200 with deployment details
        assert response.status_code == 200
        deployment = response.json()

        # Validate deployment structure
        assert "id" in deployment
        assert "agent_id" in deployment
        assert "status" in deployment
        assert "config" in deployment
        assert "created_at" in deployment
        assert "updated_at" in deployment

    @pytest.mark.asyncio
    async def test_get_deployment_not_found(self, client: AsyncClient, auth_headers):
        """Test getting deployment that doesn't exist."""
        non_existent_id = str(uuid4())

        response = await client.get(f"/deployments/{non_existent_id}", headers=auth_headers)

        # Should return 404 for non-existent deployment
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_deployment_not_owned_by_user(self, client: AsyncClient, auth_headers):
        """Test getting deployment owned by another user."""
        other_user_deployment_id = str(uuid4())

        response = await client.get(f"/deployments/{other_user_deployment_id}", headers=auth_headers)

        # Should return 404 (not found for this user)
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_deployment_invalid_uuid(self, client: AsyncClient, auth_headers):
        """Test getting deployment with invalid UUID format."""
        invalid_id = "not-a-uuid"

        response = await client.get(f"/deployments/{invalid_id}", headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_deployment_unauthenticated(self, client: AsyncClient):
        """Test getting deployment without authentication."""
        deployment_id = str(uuid4())

        response = await client.get(f"/deployments/{deployment_id}")

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_deployment_invalid_token(self, client: AsyncClient):
        """Test getting deployment with invalid token."""
        deployment_id = str(uuid4())
        invalid_headers = {"Authorization": "Bearer invalid-token"}

        response = await client.get(f"/deployments/{deployment_id}", headers=invalid_headers)

        # Should return 401 for invalid token
        assert response.status_code == 401