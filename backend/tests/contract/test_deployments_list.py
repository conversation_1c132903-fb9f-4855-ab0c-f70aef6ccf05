import pytest
from httpx import AsyncClient


class TestDeploymentsList:
    """Contract tests for GET /deployments endpoint."""

    @pytest.mark.asyncio
    async def test_get_deployments_list(self, client: AsyncClient, auth_headers):
        """Test getting list of user deployments."""
        response = await client.get("/deployments", headers=auth_headers)

        # Should return 200 with deployments list
        assert response.status_code == 200
        data = response.json()

        # Validate response structure
        assert "deployments" in data
        assert isinstance(data["deployments"], list)

        # If there are deployments, validate their structure
        if data["deployments"]:
            deployment = data["deployments"][0]
            assert "id" in deployment
            assert "agent_id" in deployment
            assert "status" in deployment
            assert "config" in deployment
            assert "created_at" in deployment
            assert "updated_at" in deployment

    @pytest.mark.asyncio
    async def test_get_deployments_unauthenticated(self, client: AsyncClient):
        """Test getting deployments list without authentication."""
        response = await client.get("/deployments")

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_deployments_invalid_token(self, client: AsyncClient):
        """Test getting deployments list with invalid token."""
        invalid_headers = {"Authorization": "Bearer invalid-token"}
        response = await client.get("/deployments", headers=invalid_headers)

        # Should return 401 for invalid token
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_deployments_empty_list(self, client: AsyncClient, auth_headers):
        """Test getting deployments list when user has no deployments."""
        response = await client.get("/deployments", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert "deployments" in data
        # For a new user, this should be empty
        assert data["deployments"] == []