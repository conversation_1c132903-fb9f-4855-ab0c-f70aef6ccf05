import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestReviewsCreate:
    """Contract tests for POST /agents/{agent_id}/reviews endpoint."""

    @pytest.mark.asyncio
    async def test_create_review_successful(self, client: AsyncClient, auth_headers):
        """Test creating a new review with valid data."""
        agent_id = str(uuid4())
        review_data = {
            "rating": 5,
            "title": "Excellent Agent",
            "comment": "This agent performs exceptionally well for my use case."
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 201 with created review
        assert response.status_code == 201
        review = response.json()

        # Validate created review structure
        assert "id" in review
        assert review["rating"] == review_data["rating"]
        assert review["title"] == review_data["title"]
        assert review["comment"] == review_data["comment"]
        assert "user" in review
        assert "created_at" in review

    @pytest.mark.asyncio
    async def test_create_review_minimal_data(self, client: AsyncClient, auth_headers):
        """Test creating a review with minimal required data."""
        agent_id = str(uuid4())
        review_data = {
            "rating": 4
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        assert response.status_code == 201
        review = response.json()
        assert review["rating"] == review_data["rating"]
        # Optional fields should be None or empty
        assert review["title"] is None or review["title"] == ""
        assert review["comment"] is None or review["comment"] == ""

    @pytest.mark.asyncio
    async def test_create_review_missing_rating(self, client: AsyncClient, auth_headers):
        """Test creating review with missing required rating field."""
        agent_id = str(uuid4())
        review_data = {
            "title": "Good Agent"
            # Missing rating
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_review_invalid_rating_low(self, client: AsyncClient, auth_headers):
        """Test creating review with rating below minimum."""
        agent_id = str(uuid4())
        review_data = {
            "rating": 0  # Below minimum of 1
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_review_invalid_rating_high(self, client: AsyncClient, auth_headers):
        """Test creating review with rating above maximum."""
        agent_id = str(uuid4())
        review_data = {
            "rating": 6  # Above maximum of 5
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_review_title_too_long(self, client: AsyncClient, auth_headers):
        """Test creating review with title exceeding maximum length."""
        agent_id = str(uuid4())
        long_title = "A" * 201  # Exceeds 200 character limit
        review_data = {
            "rating": 5,
            "title": long_title
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_review_comment_too_long(self, client: AsyncClient, auth_headers):
        """Test creating review with comment exceeding maximum length."""
        agent_id = str(uuid4())
        long_comment = "A" * 2001  # Exceeds 2000 character limit
        review_data = {
            "rating": 5,
            "comment": long_comment
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_review_invalid_agent_id(self, client: AsyncClient, auth_headers):
        """Test creating review for invalid agent ID."""
        invalid_id = "not-a-uuid"
        review_data = {
            "rating": 5
        }

        response = await client.post(f"/agents/{invalid_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_review_nonexistent_agent(self, client: AsyncClient, auth_headers):
        """Test creating review for agent that doesn't exist."""
        non_existent_id = str(uuid4())
        review_data = {
            "rating": 5
        }

        response = await client.post(f"/agents/{non_existent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 400 for invalid agent
        assert response.status_code == 400

    @pytest.mark.asyncio
    async def test_create_review_unauthenticated(self, client: AsyncClient):
        """Test creating review without authentication."""
        agent_id = str(uuid4())
        review_data = {
            "rating": 5
        }

        response = await client.post(f"/agents/{agent_id}/reviews", json=review_data)

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_create_review_duplicate(self, client: AsyncClient, auth_headers):
        """Test creating multiple reviews for same agent by same user."""
        agent_id = str(uuid4())
        review_data = {
            "rating": 5,
            "title": "First Review"
        }

        # Create first review
        response1 = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)
        assert response1.status_code == 201

        # Try to create second review
        review_data["title"] = "Second Review"
        response2 = await client.post(f"/agents/{agent_id}/reviews", json=review_data, headers=auth_headers)

        # Should return 400 for duplicate review
        assert response2.status_code == 400