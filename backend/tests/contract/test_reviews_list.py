import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestReviewsList:
    """Contract tests for GET /agents/{agent_id}/reviews endpoint."""

    @pytest.mark.asyncio
    async def test_get_agent_reviews(self, client: AsyncClient):
        """Test getting reviews for an agent."""
        agent_id = str(uuid4())

        response = await client.get(f"/agents/{agent_id}/reviews")

        # Should return 200 with reviews list
        assert response.status_code == 200
        data = response.json()

        # Validate response structure
        assert "reviews" in data
        assert "total" in data
        assert isinstance(data["reviews"], list)
        assert isinstance(data["total"], int)

        # If there are reviews, validate their structure
        if data["reviews"]:
            review = data["reviews"][0]
            assert "id" in review
            assert "rating" in review
            assert "title" in review
            assert "comment" in review
            assert "user" in review
            assert "created_at" in review

    @pytest.mark.asyncio
    async def test_get_agent_reviews_pagination(self, client: AsyncClient):
        """Test agent reviews pagination."""
        agent_id = str(uuid4())

        response = await client.get(f"/agents/{agent_id}/reviews?page=1&limit=10")

        assert response.status_code == 200
        data = response.json()
        assert "reviews" in data
        assert len(data["reviews"]) <= 10

    @pytest.mark.asyncio
    async def test_get_agent_reviews_invalid_agent_id(self, client: AsyncClient):
        """Test getting reviews for invalid agent ID."""
        invalid_id = "not-a-uuid"

        response = await client.get(f"/agents/{invalid_id}/reviews")

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_agent_reviews_nonexistent_agent(self, client: AsyncClient):
        """Test getting reviews for agent that doesn't exist."""
        non_existent_id = str(uuid4())

        response = await client.get(f"/agents/{non_existent_id}/reviews")

        # Should return 200 with empty reviews list
        assert response.status_code == 200
        data = response.json()
        assert data["reviews"] == []
        assert data["total"] == 0

    @pytest.mark.asyncio
    async def test_get_agent_reviews_invalid_page(self, client: AsyncClient):
        """Test agent reviews with invalid page number."""
        agent_id = str(uuid4())

        response = await client.get(f"/agents/{agent_id}/reviews?page=0")

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_agent_reviews_invalid_limit(self, client: AsyncClient):
        """Test agent reviews with invalid limit."""
        agent_id = str(uuid4())

        response = await client.get(f"/agents/{agent_id}/reviews?limit=150")  # Over maximum

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_agent_reviews_empty_result(self, client: AsyncClient):
        """Test agent reviews when no reviews exist."""
        agent_id = str(uuid4())

        response = await client.get(f"/agents/{agent_id}/reviews")

        assert response.status_code == 200
        data = response.json()
        assert "reviews" in data
        assert data["reviews"] == []
        assert data["total"] == 0