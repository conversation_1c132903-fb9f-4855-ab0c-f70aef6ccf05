import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestTasksCreate:
    """Contract tests for POST /tasks endpoint."""

    @pytest.mark.asyncio
    async def test_create_task_successful(self, client: AsyncClient, auth_headers):
        """Test creating a new task with valid data."""
        agent_id = str(uuid4())
        task_data = {
            "title": "Test Task",
            "description": "A test task for demonstration purposes",
            "agent_id": agent_id,
            "priority": "HIGH",
            "parameters": {
                "input_text": "Test input",
                "max_length": 100
            }
        }

        response = await client.post("/tasks", json=task_data, headers=auth_headers)

        # Should return 201 with created task
        assert response.status_code == 201
        task = response.json()

        # Validate created task structure
        assert "id" in task
        assert task["title"] == task_data["title"]
        assert task["description"] == task_data["description"]
        assert task["agent_id"] == agent_id
        assert task["priority"] == task_data["priority"]
        assert task["status"] == "PENDING"  # Default status
        assert task["parameters"] == task_data["parameters"]
        assert "created_at" in task
        assert "updated_at" in task

    @pytest.mark.asyncio
    async def test_create_task_minimal_data(self, client: AsyncClient, auth_headers):
        """Test creating a task with minimal required data."""
        agent_id = str(uuid4())
        task_data = {
            "title": "Minimal Task",
            "description": "Minimal description",
            "agent_id": agent_id
        }

        response = await client.post("/tasks", json=task_data, headers=auth_headers)

        assert response.status_code == 201
        task = response.json()
        assert task["title"] == task_data["title"]
        assert task["priority"] == "MEDIUM"  # Default priority

    @pytest.mark.asyncio
    async def test_create_task_missing_required_fields(self, client: AsyncClient, auth_headers):
        """Test creating task with missing required fields."""
        incomplete_data = {
            "title": "Incomplete Task"
            # Missing description and agent_id
        }

        response = await client.post("/tasks", json=incomplete_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_task_invalid_agent_id(self, client: AsyncClient, auth_headers):
        """Test creating task with invalid agent_id format."""
        task_data = {
            "title": "Test Task",
            "description": "Test description",
            "agent_id": "not-a-uuid"
        }

        response = await client.post("/tasks", json=task_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_task_invalid_priority(self, client: AsyncClient, auth_headers):
        """Test creating task with invalid priority."""
        agent_id = str(uuid4())
        task_data = {
            "title": "Test Task",
            "description": "Test description",
            "agent_id": agent_id,
            "priority": "INVALID_PRIORITY"
        }

        response = await client.post("/tasks", json=task_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_task_title_too_long(self, client: AsyncClient, auth_headers):
        """Test creating task with title exceeding maximum length."""
        agent_id = str(uuid4())
        long_title = "A" * 201  # Exceeds 200 character limit
        task_data = {
            "title": long_title,
            "description": "Test description",
            "agent_id": agent_id
        }

        response = await client.post("/tasks", json=task_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_task_description_too_long(self, client: AsyncClient, auth_headers):
        """Test creating task with description exceeding maximum length."""
        agent_id = str(uuid4())
        long_description = "A" * 5001  # Exceeds 5000 character limit
        task_data = {
            "title": "Test Task",
            "description": long_description,
            "agent_id": agent_id
        }

        response = await client.post("/tasks", json=task_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_task_unauthenticated(self, client: AsyncClient):
        """Test creating task without authentication."""
        agent_id = str(uuid4())
        task_data = {
            "title": "Test Task",
            "description": "Test description",
            "agent_id": agent_id
        }

        response = await client.post("/tasks", json=task_data)

        # Should return 401 for unauthenticated request
        assert response.status_code == 401