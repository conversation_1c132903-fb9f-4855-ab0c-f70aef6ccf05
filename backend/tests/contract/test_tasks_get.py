import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestTasksGet:
    """Contract tests for GET /tasks/{task_id} endpoint."""

    @pytest.mark.asyncio
    async def test_get_task_by_id(self, client: AsyncClient, auth_headers):
        """Test getting task details by ID."""
        # Assume a task exists for this user
        task_id = str(uuid4())

        response = await client.get(f"/tasks/{task_id}", headers=auth_headers)

        # Should return 200 with task details
        assert response.status_code == 200
        task = response.json()

        # Validate task structure
        assert "id" in task
        assert "title" in task
        assert "description" in task
        assert "status" in task
        assert "priority" in task
        assert "agent_id" in task
        assert "parameters" in task
        assert "created_at" in task
        assert "updated_at" in task

    @pytest.mark.asyncio
    async def test_get_task_not_found(self, client: AsyncClient, auth_headers):
        """Test getting task that doesn't exist."""
        non_existent_id = str(uuid4())

        response = await client.get(f"/tasks/{non_existent_id}", headers=auth_headers)

        # Should return 404 for non-existent task
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_task_not_owned_by_user(self, client: AsyncClient, auth_headers):
        """Test getting task owned by another user."""
        other_user_task_id = str(uuid4())

        response = await client.get(f"/tasks/{other_user_task_id}", headers=auth_headers)

        # Should return 404 (not found for this user)
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_task_invalid_uuid(self, client: AsyncClient, auth_headers):
        """Test getting task with invalid UUID format."""
        invalid_id = "not-a-uuid"

        response = await client.get(f"/tasks/{invalid_id}", headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_task_unauthenticated(self, client: AsyncClient):
        """Test getting task without authentication."""
        task_id = str(uuid4())

        response = await client.get(f"/tasks/{task_id}")

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_task_invalid_token(self, client: AsyncClient):
        """Test getting task with invalid token."""
        task_id = str(uuid4())
        invalid_headers = {"Authorization": "Bearer invalid-token"}

        response = await client.get(f"/tasks/{task_id}", headers=invalid_headers)

        # Should return 401 for invalid token
        assert response.status_code == 401