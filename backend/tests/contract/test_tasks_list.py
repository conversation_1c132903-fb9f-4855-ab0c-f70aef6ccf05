import pytest
from httpx import AsyncClient


class TestTasksList:
    """Contract tests for GET /tasks endpoint."""

    @pytest.mark.asyncio
    async def test_get_tasks_list(self, client: AsyncClient, auth_headers):
        """Test getting list of user tasks."""
        response = await client.get("/tasks", headers=auth_headers)

        # Should return 200 with tasks list
        assert response.status_code == 200
        data = response.json()

        # Validate response structure
        assert "tasks" in data
        assert "total" in data
        assert isinstance(data["tasks"], list)
        assert isinstance(data["total"], int)

        # If there are tasks, validate their structure
        if data["tasks"]:
            task = data["tasks"][0]
            assert "id" in task
            assert "title" in task
            assert "description" in task
            assert "status" in task
            assert "priority" in task
            assert "agent_id" in task
            assert "created_at" in task
            assert "updated_at" in task

    @pytest.mark.asyncio
    async def test_get_tasks_with_status_filter(self, client: AsyncClient, auth_headers):
        """Test getting tasks filtered by status."""
        response = await client.get("/tasks?status=PENDING", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert "tasks" in data
        assert isinstance(data["tasks"], list)

        # All returned tasks should have the filtered status
        for task in data["tasks"]:
            assert task["status"] == "PENDING"

    @pytest.mark.asyncio
    async def test_get_tasks_pagination(self, client: AsyncClient, auth_headers):
        """Test tasks list pagination."""
        response = await client.get("/tasks?page=1&limit=5", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert "tasks" in data
        assert len(data["tasks"]) <= 5

    @pytest.mark.asyncio
    async def test_get_tasks_invalid_status(self, client: AsyncClient, auth_headers):
        """Test tasks list with invalid status filter."""
        response = await client.get("/tasks?status=INVALID_STATUS", headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_tasks_invalid_page(self, client: AsyncClient, auth_headers):
        """Test tasks list with invalid page number."""
        response = await client.get("/tasks?page=0", headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_tasks_invalid_limit(self, client: AsyncClient, auth_headers):
        """Test tasks list with invalid limit."""
        response = await client.get("/tasks?limit=150", headers=auth_headers)  # Over maximum

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_get_tasks_unauthenticated(self, client: AsyncClient):
        """Test getting tasks list without authentication."""
        response = await client.get("/tasks")

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_tasks_empty_list(self, client: AsyncClient, auth_headers):
        """Test getting tasks list when user has no tasks."""
        response = await client.get("/tasks", headers=auth_headers)

        assert response.status_code == 200
        data = response.json()
        assert "tasks" in data
        # For a new user, this should be empty
        assert data["tasks"] == []