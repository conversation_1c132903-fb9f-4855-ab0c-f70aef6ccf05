import pytest
from httpx import AsyncClient
from uuid import uuid4


class TestTasksUpdate:
    """Contract tests for PUT /tasks/{task_id} endpoint."""

    @pytest.mark.asyncio
    async def test_update_task_status(self, client: AsyncClient, auth_headers):
        """Test updating task status."""
        task_id = str(uuid4())
        update_data = {
            "status": "RUNNING"
        }

        response = await client.put(f"/tasks/{task_id}", json=update_data, headers=auth_headers)

        # Should return 200 with updated task
        assert response.status_code == 200
        task = response.json()
        assert task["status"] == "RUNNING"

    @pytest.mark.asyncio
    async def test_update_task_priority(self, client: AsyncClient, auth_headers):
        """Test updating task priority."""
        task_id = str(uuid4())
        update_data = {
            "priority": "URGENT"
        }

        response = await client.put(f"/tasks/{task_id}", json=update_data, headers=auth_headers)

        assert response.status_code == 200
        task = response.json()
        assert task["priority"] == "URGENT"

    @pytest.mark.asyncio
    async def test_update_task_multiple_fields(self, client: AsyncClient, auth_headers):
        """Test updating multiple task fields at once."""
        task_id = str(uuid4())
        update_data = {
            "status": "COMPLETED",
            "priority": "LOW"
        }

        response = await client.put(f"/tasks/{task_id}", json=update_data, headers=auth_headers)

        assert response.status_code == 200
        task = response.json()
        assert task["status"] == "COMPLETED"
        assert task["priority"] == "LOW"

    @pytest.mark.asyncio
    async def test_update_task_empty_data(self, client: AsyncClient, auth_headers):
        """Test updating task with empty data."""
        task_id = str(uuid4())
        update_data = {}

        response = await client.put(f"/tasks/{task_id}", json=update_data, headers=auth_headers)

        # Should return 200 (no changes made)
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_update_task_invalid_status(self, client: AsyncClient, auth_headers):
        """Test updating task with invalid status."""
        task_id = str(uuid4())
        update_data = {
            "status": "INVALID_STATUS"
        }

        response = await client.put(f"/tasks/{task_id}", json=update_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_update_task_invalid_priority(self, client: AsyncClient, auth_headers):
        """Test updating task with invalid priority."""
        task_id = str(uuid4())
        update_data = {
            "priority": "INVALID_PRIORITY"
        }

        response = await client.put(f"/tasks/{task_id}", json=update_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_update_task_not_found(self, client: AsyncClient, auth_headers):
        """Test updating task that doesn't exist."""
        non_existent_id = str(uuid4())
        update_data = {
            "status": "COMPLETED"
        }

        response = await client.put(f"/tasks/{non_existent_id}", json=update_data, headers=auth_headers)

        # Should return 404 for non-existent task
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_task_not_owned_by_user(self, client: AsyncClient, auth_headers):
        """Test updating task owned by another user."""
        other_user_task_id = str(uuid4())
        update_data = {
            "status": "COMPLETED"
        }

        response = await client.put(f"/tasks/{other_user_task_id}", json=update_data, headers=auth_headers)

        # Should return 404 (not found for this user)
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_task_invalid_uuid(self, client: AsyncClient, auth_headers):
        """Test updating task with invalid UUID format."""
        invalid_id = "not-a-uuid"
        update_data = {
            "status": "COMPLETED"
        }

        response = await client.put(f"/tasks/{invalid_id}", json=update_data, headers=auth_headers)

        # Should return 422 for validation error
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_update_task_unauthenticated(self, client: AsyncClient):
        """Test updating task without authentication."""
        task_id = str(uuid4())
        update_data = {
            "status": "COMPLETED"
        }

        response = await client.put(f"/tasks/{task_id}", json=update_data)

        # Should return 401 for unauthenticated request
        assert response.status_code == 401