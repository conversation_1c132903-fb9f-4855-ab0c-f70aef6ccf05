import pytest
from httpx import AsyncClient


class TestUsersMeGet:
    """Contract tests for GET /users/me endpoint."""

    @pytest.mark.asyncio
    async def test_get_current_user_profile(self, client: AsyncClient, auth_headers):
        """Test getting current user profile with valid authentication."""
        response = await client.get("/users/me", headers=auth_headers)

        # Should return 200 with user profile
        assert response.status_code == 200
        user = response.json()

        # Validate user object structure
        assert "id" in user
        assert "email" in user
        assert "username" in user
        assert "full_name" in user
        assert "avatar_url" in user
        assert "subscription_tier" in user
        assert user["subscription_tier"] in ["FREE", "PRO", "ENTERPRISE"]
        assert "created_at" in user
        assert "is_active" in user
        assert isinstance(user["is_active"], bool)

    @pytest.mark.asyncio
    async def test_get_current_user_unauthenticated(self, client: AsyncClient):
        """Test getting current user profile without authentication."""
        response = await client.get("/users/me")

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_user_invalid_token(self, client: AsyncClient):
        """Test getting current user profile with invalid token."""
        invalid_headers = {"Authorization": "Bearer invalid-token"}
        response = await client.get("/users/me", headers=invalid_headers)

        # Should return 401 for invalid token
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_get_current_user_expired_token(self, client: AsyncClient):
        """Test getting current user profile with expired token."""
        expired_headers = {"Authorization": "Bearer expired-token"}
        response = await client.get("/users/me", headers=expired_headers)

        # Should return 401 for expired token
        assert response.status_code == 401