import pytest
from httpx import AsyncClient


class TestUsersMePut:
    """Contract tests for PUT /users/me endpoint."""

    @pytest.mark.asyncio
    async def test_update_current_user_profile(self, client: AsyncClient, auth_headers):
        """Test updating current user profile with valid data."""
        update_data = {
            "full_name": "Updated Name",
            "username": "updatedusername",
            "avatar_url": "https://example.com/avatar.jpg"
        }

        response = await client.put("/users/me", json=update_data, headers=auth_headers)

        # Should return 200 with updated user data
        assert response.status_code == 200
        user = response.json()

        # Validate updated fields
        assert user["full_name"] == update_data["full_name"]
        assert user["username"] == update_data["username"]
        assert user["avatar_url"] == update_data["avatar_url"]

        # Validate other required fields are still present
        assert "id" in user
        assert "email" in user
        assert "subscription_tier" in user
        assert "created_at" in user
        assert "is_active" in user

    @pytest.mark.asyncio
    async def test_update_current_user_partial_data(self, client: AsyncClient, auth_headers):
        """Test updating current user profile with partial data."""
        update_data = {
            "full_name": "Partially Updated Name"
        }

        response = await client.put("/users/me", json=update_data, headers=auth_headers)

        # Should return 200 with updated user data
        assert response.status_code == 200
        user = response.json()

        # Validate updated field
        assert user["full_name"] == update_data["full_name"]

        # Validate other fields are unchanged
        assert "username" in user
        assert "email" in user

    @pytest.mark.asyncio
    async def test_update_current_user_empty_data(self, client: AsyncClient, auth_headers):
        """Test updating current user profile with empty data."""
        update_data = {}

        response = await client.put("/users/me", json=update_data, headers=auth_headers)

        # Should return 200 (no changes made)
        assert response.status_code == 200
        user = response.json()

        # Validate user object structure is maintained
        assert "id" in user
        assert "email" in user
        assert "username" in user
        assert "full_name" in user

    @pytest.mark.asyncio
    async def test_update_current_user_unauthenticated(self, client: AsyncClient):
        """Test updating current user profile without authentication."""
        update_data = {
            "full_name": "New Name"
        }

        response = await client.put("/users/me", json=update_data)

        # Should return 401 for unauthenticated request
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_update_current_user_invalid_token(self, client: AsyncClient):
        """Test updating current user profile with invalid token."""
        update_data = {
            "full_name": "New Name"
        }
        invalid_headers = {"Authorization": "Bearer invalid-token"}

        response = await client.put("/users/me", json=update_data, headers=invalid_headers)

        # Should return 401 for invalid token
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_update_current_user_username_conflict(self, client: AsyncClient, auth_headers):
        """Test updating current user profile with username that already exists."""
        update_data = {
            "username": "existingusername"
        }

        response = await client.put("/users/me", json=update_data, headers=auth_headers)

        # Should return 400 for username conflict
        assert response.status_code == 400