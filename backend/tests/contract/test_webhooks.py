import pytest
from httpx import AsyncClient


class TestWebhooks:
    """Contract tests for POST /webhooks endpoint."""

    @pytest.mark.asyncio
    async def test_webhook_task_completed(self, client: AsyncClient):
        """Test webhook for task completed event."""
        webhook_data = {
            "event": "task.completed",
            "data": {
                "task_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "COMPLETED",
                "result": {
                    "output": "Task completed successfully",
                    "duration": 120
                }
            }
        }

        response = await client.post("/webhooks", json=webhook_data)

        # Should return 200 for successful webhook processing
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_task_failed(self, client: AsyncClient):
        """Test webhook for task failed event."""
        webhook_data = {
            "event": "task.failed",
            "data": {
                "task_id": "123e4567-e89b-12d3-a456-426614174000",
                "status": "FAILED",
                "error": {
                    "message": "Task execution failed",
                    "code": "EXECUTION_ERROR"
                }
            }
        }

        response = await client.post("/webhooks", json=webhook_data)

        # Should return 200 for successful webhook processing
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_deployment_status_changed(self, client: AsyncClient):
        """Test webhook for deployment status change."""
        webhook_data = {
            "event": "deployment.status_changed",
            "data": {
                "deployment_id": "123e4567-e89b-12d3-a456-426614174000",
                "agent_id": "456e7890-e89b-12d3-a456-426614174000",
                "old_status": "STARTING",
                "new_status": "RUNNING",
                "timestamp": "2025-01-14T10:30:00Z"
            }
        }

        response = await client.post("/webhooks", json=webhook_data)

        # Should return 200 for successful webhook processing
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_missing_event(self, client: AsyncClient):
        """Test webhook with missing event field."""
        webhook_data = {
            "data": {
                "task_id": "123e4567-e89b-12d3-a456-426614174000"
            }
        }

        response = await client.post("/webhooks", json=webhook_data)

        # Should return 200 (webhook processed, even if event is unknown)
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_missing_data(self, client: AsyncClient):
        """Test webhook with missing data field."""
        webhook_data = {
            "event": "task.completed"
        }

        response = await client.post("/webhooks", json=webhook_data)

        # Should return 200 (webhook processed)
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_empty_payload(self, client: AsyncClient):
        """Test webhook with empty payload."""
        webhook_data = {}

        response = await client.post("/webhooks", json=webhook_data)

        # Should return 200 (webhook processed)
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_large_payload(self, client: AsyncClient):
        """Test webhook with large payload."""
        large_data = "x" * 10000  # Large string
        webhook_data = {
            "event": "test.large_payload",
            "data": {
                "content": large_data
            }
        }

        response = await client.post("/webhooks", json=webhook_data)

        # Should return 200 for successful processing
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_webhook_invalid_json(self, client: AsyncClient):
        """Test webhook with invalid JSON (should not reach this endpoint if JSON is invalid)."""
        # This test is more about the framework handling invalid JSON
        # In practice, invalid JSON would be rejected before reaching the endpoint
        pass

    @pytest.mark.asyncio
    async def test_webhook_unsupported_content_type(self, client: AsyncClient):
        """Test webhook with unsupported content type."""
        # This would be handled by the framework before reaching the endpoint
        # Sending form data instead of JSON
        response = await client.post("/webhooks", data={"event": "test"})

        # Framework should handle content-type validation
        # Either 400 for bad request or 200 if framework converts
        assert response.status_code in [200, 400]