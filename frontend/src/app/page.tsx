export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex">
        <h1 className="text-4xl font-bold text-center">
          Welcome to DahoAI
        </h1>
      </div>

      <div className="relative flex place-items-center">
        <div className="text-center">
          <h2 className="mb-3 text-2xl font-semibold">
            AI Agents Platform
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Deploy and manage AI agents for your business needs
          </p>
        </div>
      </div>

      <div className="mb-32 grid text-center lg:max-w-5xl lg:w-full lg:mb-0 lg:grid-cols-4 lg:text-left">
        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h2 className="mb-3 text-2xl font-semibold">
            Marketplace
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Browse and discover AI agents for your needs
          </p>
        </div>

        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h2 className="mb-3 text-2xl font-semibold">
            Deploy
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Deploy agents with custom configurations
          </p>
        </div>

        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h2 className="mb-3 text-2xl font-semibold">
            Monitor
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Track performance and manage your agents
          </p>
        </div>

        <div className="group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100">
          <h2 className="mb-3 text-2xl font-semibold">
            Collaborate
          </h2>
          <p className="m-0 max-w-[30ch] text-sm opacity-50">
            Work with your team on agent deployments
          </p>
        </div>
      </div>
    </main>
  )
}
