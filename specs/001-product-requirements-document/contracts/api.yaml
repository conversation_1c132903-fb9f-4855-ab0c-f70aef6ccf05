openapi: 3.0.3
info:
  title: DahoAI Platform API
  version: 1.0.0
  description: RESTful API for the DahoAI AI Agents Platform
  contact:
    name: DahoAI Support
    email: <EMAIL>

servers:
  - url: https://api.dahoai.com/v1
    description: Production server
  - url: https://staging-api.dahoai.com/v1
    description: Staging server

security:
  - bearerAuth: []

paths:
  # Authentication
  /auth/login:
    post:
      summary: User login
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
      responses:
        "200":
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                  token_type:
                    type: string
                    example: bearer
                  expires_in:
                    type: integer
                  user:
                    $ref: "#/components/schemas/User"
        "401":
          description: Invalid credentials

  /auth/register:
    post:
      summary: User registration
      tags: [Authentication]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - full_name
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  minLength: 8
                full_name:
                  type: string
                  minLength: 2
                username:
                  type: string
                  minLength: 3
      responses:
        "201":
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
        "400":
          description: Validation error

  # User Management
  /users/me:
    get:
      summary: Get current user profile
      tags: [Users]
      security:
        - bearerAuth: []
      responses:
        "200":
          description: User profile
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"
    put:
      summary: Update current user profile
      tags: [Users]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                full_name:
                  type: string
                username:
                  type: string
                avatar_url:
                  type: string
      responses:
        "200":
          description: Profile updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/User"

  # Agent Marketplace
  /agents:
    get:
      summary: List agents
      tags: [Agents]
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: search
          in: query
          schema:
            type: string
        - name: featured
          in: query
          schema:
            type: boolean
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        "200":
          description: List of agents
          content:
            application/json:
              schema:
                type: object
                properties:
                  agents:
                    type: array
                    items:
                      $ref: "#/components/schemas/Agent"
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
    post:
      summary: Create new agent
      tags: [Agents]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AgentCreate"
      responses:
        "201":
          description: Agent created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Agent"

  /agents/{agent_id}:
    get:
      summary: Get agent details
      tags: [Agents]
      parameters:
        - name: agent_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Agent details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Agent"
        "404":
          description: Agent not found

  # Agent Deployment
  /deployments:
    get:
      summary: List user deployments
      tags: [Deployments]
      security:
        - bearerAuth: []
      responses:
        "200":
          description: List of deployments
          content:
            application/json:
              schema:
                type: object
                properties:
                  deployments:
                    type: array
                    items:
                      $ref: "#/components/schemas/Deployment"
    post:
      summary: Deploy an agent
      tags: [Deployments]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - agent_id
              properties:
                agent_id:
                  type: string
                  format: uuid
                config:
                  type: object
      responses:
        "201":
          description: Deployment initiated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Deployment"
        "400":
          description: Invalid configuration

  /deployments/{deployment_id}:
    get:
      summary: Get deployment status
      tags: [Deployments]
      security:
        - bearerAuth: []
      parameters:
        - name: deployment_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Deployment status
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Deployment"
    delete:
      summary: Stop deployment
      tags: [Deployments]
      security:
        - bearerAuth: []
      parameters:
        - name: deployment_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "204":
          description: Deployment stopped

  # Task Management
  /tasks:
    get:
      summary: List user tasks
      tags: [Tasks]
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          schema:
            type: string
            enum: [PENDING, RUNNING, COMPLETED, FAILED, CANCELLED]
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        "200":
          description: List of tasks
          content:
            application/json:
              schema:
                type: object
                properties:
                  tasks:
                    type: array
                    items:
                      $ref: "#/components/schemas/Task"
                  total:
                    type: integer
    post:
      summary: Create new task
      tags: [Tasks]
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - title
                - description
                - agent_id
              properties:
                title:
                  type: string
                  minLength: 1
                  maxLength: 200
                description:
                  type: string
                  minLength: 1
                  maxLength: 5000
                agent_id:
                  type: string
                  format: uuid
                priority:
                  type: string
                  enum: [LOW, MEDIUM, HIGH, URGENT]
                  default: MEDIUM
                parameters:
                  type: object
      responses:
        "201":
          description: Task created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"

  /tasks/{task_id}:
    get:
      summary: Get task details
      tags: [Tasks]
      security:
        - bearerAuth: []
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        "200":
          description: Task details
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"
    put:
      summary: Update task
      tags: [Tasks]
      security:
        - bearerAuth: []
      parameters:
        - name: task_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: string
                  enum: [PENDING, RUNNING, COMPLETED, FAILED, CANCELLED]
                priority:
                  type: string
                  enum: [LOW, MEDIUM, HIGH, URGENT]
      responses:
        "200":
          description: Task updated
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Task"

  # Reviews
  /agents/{agent_id}/reviews:
    get:
      summary: Get agent reviews
      tags: [Reviews]
      parameters:
        - name: agent_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
      responses:
        "200":
          description: List of reviews
          content:
            application/json:
              schema:
                type: object
                properties:
                  reviews:
                    type: array
                    items:
                      $ref: "#/components/schemas/Review"
                  total:
                    type: integer
    post:
      summary: Create agent review
      tags: [Reviews]
      security:
        - bearerAuth: []
      parameters:
        - name: agent_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - rating
              properties:
                rating:
                  type: integer
                  minimum: 1
                  maximum: 5
                title:
                  type: string
                  maxLength: 200
                comment:
                  type: string
                  maxLength: 2000
      responses:
        "201":
          description: Review created
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Review"

  # Webhooks
  /webhooks:
    post:
      summary: Webhook endpoint for external integrations
      tags: [Webhooks]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                event:
                  type: string
                data:
                  type: object
      responses:
        "200":
          description: Webhook processed
      security:
        - apiKey: []

components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        username:
          type: string
        full_name:
          type: string
        avatar_url:
          type: string
        subscription_tier:
          type: string
          enum: [FREE, PRO, ENTERPRISE]
        created_at:
          type: string
          format: date-time
        is_active:
          type: boolean

    Agent:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        category:
          type: string
        version:
          type: string
        creator:
          $ref: "#/components/schemas/User"
        pricing_model:
          type: string
          enum: [FREE, PAID, SUBSCRIPTION]
        price:
          type: number
        currency:
          type: string
        tags:
          type: array
          items:
            type: string
        capabilities:
          type: object
        requirements:
          type: object
        rating_average:
          type: number
        rating_count:
          type: integer
        download_count:
          type: integer
        created_at:
          type: string
          format: date-time

    AgentCreate:
      type: object
      required:
        - name
        - description
        - category
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 100
        description:
          type: string
          minLength: 10
          maxLength: 2000
        category:
          type: string
        tags:
          type: array
          items:
            type: string
        capabilities:
          type: object
        requirements:
          type: object
        pricing_model:
          type: string
          enum: [FREE, PAID, SUBSCRIPTION]
        price:
          type: number
        currency:
          type: string
          default: USD

    Task:
      type: object
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        description:
          type: string
        creator:
          $ref: "#/components/schemas/User"
        assigned_agent:
          $ref: "#/components/schemas/Agent"
        status:
          type: string
          enum: [PENDING, RUNNING, COMPLETED, FAILED, CANCELLED]
        priority:
          type: string
          enum: [LOW, MEDIUM, HIGH, URGENT]
        parameters:
          type: object
        result:
          type: object
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        started_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time

    Deployment:
      type: object
      properties:
        id:
          type: string
          format: uuid
        user:
          $ref: "#/components/schemas/User"
        agent:
          $ref: "#/components/schemas/Agent"
        version:
          type: string
        status:
          type: string
          enum: [DEPLOYING, RUNNING, STOPPED, FAILED]
        config:
          type: object
        endpoint_url:
          type: string
        deployed_at:
          type: string
          format: date-time
        health_status:
          type: string
          enum: [HEALTHY, UNHEALTHY, UNKNOWN]

    Review:
      type: object
      properties:
        id:
          type: string
          format: uuid
        agent:
          $ref: "#/components/schemas/Agent"
        user:
          $ref: "#/components/schemas/User"
        rating:
          type: integer
          minimum: 1
          maximum: 5
        title:
          type: string
        comment:
          type: string
        created_at:
          type: string
          format: date-time
        is_verified:
          type: boolean
        helpful_votes:
          type: integer

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key
