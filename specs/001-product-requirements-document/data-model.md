# Data Model: DahoAI AI Agents Platform

## Overview
The DahoAI platform requires a comprehensive data model to support user management, agent marketplace, task execution, and multi-tenant architecture. The model is designed for PostgreSQL with support for complex relationships and high concurrency.

## Core Entities

### User
**Purpose**: Represents platform users with authentication and profile information
**Fields**:
- `id` (UUID, Primary Key)
- `email` (String, Unique, Required)
- `username` (String, Unique, Optional)
- `password_hash` (String, Required)
- `full_name` (String, Optional)
- `avatar_url` (String, Optional)
- `email_verified` (Boolean, Default: False)
- `created_at` (Timestamp, Required)
- `updated_at` (Timestamp, Required)
- `last_login_at` (Timestamp, Optional)
- `is_active` (Boolean, Default: True)
- `subscription_tier` (Enum: FREE, PRO, ENTERPRISE, Default: FREE)
- `subscription_expires_at` (Timestamp, Optional)

**Relationships**:
- One-to-Many: Organizations (member)
- One-to-Many: Agents (created)
- One-to-Many: Tasks (created)
- One-to-Many: Reviews (written)

**Validation Rules**:
- Email must be valid format
- Password must be 8+ characters
- Username must be 3-30 characters if provided

### Organization
**Purpose**: Supports team and enterprise accounts
**Fields**:
- `id` (UUID, Primary Key)
- `name` (String, Required)
- `description` (String, Optional)
- `owner_id` (UUID, Foreign Key → User.id, Required)
- `created_at` (Timestamp, Required)
- `updated_at` (Timestamp, Required)
- `is_active` (Boolean, Default: True)
- `max_members` (Integer, Optional)
- `billing_email` (String, Optional)

**Relationships**:
- Many-to-One: User (owner)
- One-to-Many: OrganizationMembers
- One-to-Many: Agents (shared)

**Validation Rules**:
- Name must be 2-100 characters
- Max members must be positive if specified

### OrganizationMember
**Purpose**: Junction table for organization membership with roles
**Fields**:
- `id` (UUID, Primary Key)
- `organization_id` (UUID, Foreign Key → Organization.id, Required)
- `user_id` (UUID, Foreign Key → User.id, Required)
- `role` (Enum: OWNER, ADMIN, MEMBER, Default: MEMBER)
- `joined_at` (Timestamp, Required)
- `invited_by` (UUID, Foreign Key → User.id, Optional)

**Relationships**:
- Many-to-One: Organization
- Many-to-One: User (member)
- Many-to-One: User (inviter)

**Validation Rules**:
- Unique constraint on (organization_id, user_id)
- Cannot have multiple owners per organization

### Agent
**Purpose**: Represents AI agents available in the marketplace
**Fields**:
- `id` (UUID, Primary Key)
- `name` (String, Required)
- `description` (String, Required)
- `category` (String, Required)
- `version` (String, Required)
- `creator_id` (UUID, Foreign Key → User.id, Required)
- `organization_id` (UUID, Foreign Key → Organization.id, Optional)
- `is_public` (Boolean, Default: True)
- `is_featured` (Boolean, Default: False)
- `pricing_model` (Enum: FREE, PAID, SUBSCRIPTION, Required)
- `price` (Decimal, Optional)
- `currency` (String, Default: 'USD')
- `tags` (JSON Array, Optional)
- `capabilities` (JSON Object, Required)
- `requirements` (JSON Object, Optional)
- `created_at` (Timestamp, Required)
- `updated_at` (Timestamp, Required)
- `is_active` (Boolean, Default: True)
- `download_count` (Integer, Default: 0)
- `rating_average` (Decimal, Default: 0.0)
- `rating_count` (Integer, Default: 0)

**Relationships**:
- Many-to-One: User (creator)
- Many-to-One: Organization
- One-to-Many: AgentVersions
- One-to-Many: Reviews
- One-to-Many: Deployments

**Validation Rules**:
- Name must be 2-100 characters
- Description must be 10-2000 characters
- Version must follow semantic versioning
- Price must be positive if pricing_model is PAID

### AgentVersion
**Purpose**: Tracks different versions of agents
**Fields**:
- `id` (UUID, Primary Key)
- `agent_id` (UUID, Foreign Key → Agent.id, Required)
- `version` (String, Required)
- `changelog` (Text, Optional)
- `config_schema` (JSON Object, Required)
- `created_at` (Timestamp, Required)
- `is_current` (Boolean, Default: False)

**Relationships**:
- Many-to-One: Agent

**Validation Rules**:
- Version must follow semantic versioning
- Only one version can be current per agent

### Task
**Purpose**: Represents user tasks assigned to agents
**Fields**:
- `id` (UUID, Primary Key)
- `title` (String, Required)
- `description` (Text, Required)
- `creator_id` (UUID, Foreign Key → User.id, Required)
- `assigned_agent_id` (UUID, Foreign Key → Agent.id, Required)
- `status` (Enum: PENDING, RUNNING, COMPLETED, FAILED, CANCELLED, Default: PENDING)
- `priority` (Enum: LOW, MEDIUM, HIGH, URGENT, Default: MEDIUM)
- `parameters` (JSON Object, Optional)
- `result` (JSON Object, Optional)
- `error_message` (Text, Optional)
- `created_at` (Timestamp, Required)
- `updated_at` (Timestamp, Required)
- `started_at` (Timestamp, Optional)
- `completed_at` (Timestamp, Optional)
- `estimated_duration` (Integer, Optional) // seconds

**Relationships**:
- Many-to-One: User (creator)
- Many-to-One: Agent (assigned)
- One-to-Many: TaskLogs

**Validation Rules**:
- Title must be 1-200 characters
- Description must be 1-5000 characters
- Result and error_message are mutually exclusive

### TaskLog
**Purpose**: Tracks execution logs for tasks
**Fields**:
- `id` (UUID, Primary Key)
- `task_id` (UUID, Foreign Key → Task.id, Required)
- `level` (Enum: DEBUG, INFO, WARNING, ERROR, Required)
- `message` (Text, Required)
- `timestamp` (Timestamp, Required)
- `metadata` (JSON Object, Optional)

**Relationships**:
- Many-to-One: Task

### Review
**Purpose**: User reviews and ratings for agents
**Fields**:
- `id` (UUID, Primary Key)
- `agent_id` (UUID, Foreign Key → Agent.id, Required)
- `user_id` (UUID, Foreign Key → User.id, Required)
- `rating` (Integer, Required) // 1-5 stars
- `title` (String, Optional)
- `comment` (Text, Optional)
- `created_at` (Timestamp, Required)
- `updated_at` (Timestamp, Required)
- `is_verified` (Boolean, Default: False) // purchased/used agent
- `helpful_votes` (Integer, Default: 0)

**Relationships**:
- Many-to-One: Agent
- Many-to-One: User

**Validation Rules**:
- Rating must be 1-5
- Title must be 1-200 characters if provided
- Comment must be 1-2000 characters if provided
- Unique constraint on (agent_id, user_id)

### Deployment
**Purpose**: Tracks agent deployments by users
**Fields**:
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key → User.id, Required)
- `agent_id` (UUID, Foreign Key → Agent.id, Required)
- `version` (String, Required)
- `status` (Enum: DEPLOYING, RUNNING, STOPPED, FAILED, Required)
- `config` (JSON Object, Required)
- `endpoint_url` (String, Optional)
- `deployed_at` (Timestamp, Required)
- `last_health_check` (Timestamp, Optional)
- `health_status` (Enum: HEALTHY, UNHEALTHY, UNKNOWN, Optional)

**Relationships**:
- Many-to-One: User
- Many-to-One: Agent

**Validation Rules**:
- Endpoint URL must be valid if provided
- Config must match agent's config schema

### Subscription
**Purpose**: Manages user subscriptions and billing
**Fields**:
- `id` (UUID, Primary Key)
- `user_id` (UUID, Foreign Key → User.id, Required)
- `tier` (Enum: FREE, PRO, ENTERPRISE, Required)
- `status` (Enum: ACTIVE, CANCELLED, EXPIRED, PAST_DUE, Required)
- `current_period_start` (Timestamp, Required)
- `current_period_end` (Timestamp, Required)
- `cancel_at_period_end` (Boolean, Default: False)
- `stripe_subscription_id` (String, Optional)
- `created_at` (Timestamp, Required)
- `updated_at` (Timestamp, Required)

**Relationships**:
- Many-to-One: User

**Validation Rules**:
- Stripe subscription ID must be provided for paid tiers
- Period end must be after period start

## Database Design Considerations

### Indexes
- Users: email, username, subscription_tier, created_at
- Agents: creator_id, category, is_featured, rating_average, created_at
- Tasks: creator_id, assigned_agent_id, status, created_at
- Reviews: agent_id, user_id, rating, created_at
- Deployments: user_id, agent_id, status

### Constraints
- Foreign key constraints on all relationships
- Check constraints for enum values
- Unique constraints where specified
- Not null constraints for required fields

### Performance Considerations
- Partitioning for large tables (tasks, task_logs) by date
- Connection pooling for high concurrency
- Read replicas for analytics queries
- Archival strategy for old task logs

### Security Considerations
- Row Level Security (RLS) for multi-tenant data
- Encryption for sensitive fields
- Audit logging for critical operations
- API rate limiting integration